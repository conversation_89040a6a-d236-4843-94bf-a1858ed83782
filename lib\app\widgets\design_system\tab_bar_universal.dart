import 'package:flutter/material.dart';

class TabBarUniversal extends StatelessWidget {
  final Function(int)? onTap;
  final TabController? controller;
  final List<Widget> tabs;

  const TabBarUniversal({
    super.key,
    this.onTap,
    this.controller,
    required this.tabs,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: TabBar(
        labelPadding: const EdgeInsets.symmetric(
          horizontal: 5,
        ),
        padding: EdgeInsets.zero,
        overlayColor: const WidgetStatePropertyAll(Colors.transparent),
        onTap: onTap,
        tabAlignment: TabAlignment.start,
        controller: controller,
        isScrollable: true,
        indicatorColor: Colors.transparent,
        dividerColor: Colors.transparent,
        tabs: tabs,
      ),
    );
  }
}
