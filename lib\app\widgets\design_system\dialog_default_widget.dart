import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../themes/app_colors.dart';
import '../responsive/is_mobile.dart';
import 'text_default.dart';

class DialogDefaultWidget extends StatelessWidget {
  final String title;
  final Widget form;
  final double? height;
  final Color? backgroundColor;
  final Color? titleColor;
  final void Function()? pressBack;

  const DialogDefaultWidget({
    Key? key,
    required this.title,
    required this.form,
    this.height,
    this.pressBack,
    this.backgroundColor,
    this.titleColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: backgroundColor ?? AppColors.secondary,
      child: SizedBox(
        width: isMobile(context)
            ? MediaQuery.of(context).size.width * 0.8
            : MediaQuery.of(context).size.width * 0.6,
        height: height,
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                children: [
                  IconButton.outlined(
                    onPressed: () {
                      if (pressBack != null) pressBack!();
                      Get.back();
                    },
                    icon: const Icon(
                      Icons.close,
                      color: AppColors.cardSecondaryLight,
                    ),
                  ),
                  const SizedBox(width: 5),
                  Expanded(
                    child: TextDefault(
                      text: title,
                      fontWeight: FontWeight.w600,
                      color: titleColor,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 30),
              form,
            ],
          ),
        ),
      ),
    );
  }
}
