import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../shared/mixins/loader_manager.dart';
import '../../../shared/utils/app_snackbar.dart';
import 'send_email_recover_password/send_email_recover_password_page.dart';

class RecoverPasswordController extends GetxController with LoaderManagerMixin {
  final emailController = TextEditingController();

  @override
  void onReady() {
    emailController.addListener(update);
    super.onReady();
  }

  @override
  void onClose() {
    emailController.dispose();
    super.onClose();
  }

  Future<void> resetPassword() async {
    changeLoading(true);

    if (emailController.text.trim() != '<EMAIL>') {
      AppSnackbar.to.show('Esse não é o e-mail admin');
    } else {
      await Supabase.instance.client.auth.resetPasswordForEmail(
        emailController.text.trim(),
        redirectTo: 'https://appdosaprovados.web.app',
      );

      Get.offNamed(
        SendEmailRecoverPasswordPage.route,
        arguments: emailController.text.trim(),
      );
    }

    changeLoading(false);
  }
}
