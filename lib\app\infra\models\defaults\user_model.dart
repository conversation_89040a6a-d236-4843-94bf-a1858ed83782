import 'dart:convert';

class UserModel {
  int? id;
  String? authId;
  final String name;
  final String email;
  final String phone;
  final DateTime createdAt;
  final List<dynamic>? contestsLiberated;
  bool isActive;

  UserModel({
    this.id,
    this.authId,
    required this.name,
    required this.email,
    required this.phone,
    required this.isActive,
    required this.createdAt,
    this.contestsLiberated,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    if (id != null) result.addAll({'id': id});

    result.addAll({'name': name});
    result.addAll({'auth_id': authId});
    result.addAll({'email': email});
    result.addAll({'phone': phone});
    result.addAll({'is_active': phone});
    result.addAll({'created_at': createdAt.toIso8601String()});
    result.addAll({'created_at': createdAt.toIso8601String()});

    return result;
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'],
      authId: map['auth_id'],
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      isActive: map['is_active'] ?? false,
      createdAt: DateTime.parse(map['created_at']),
    );
  }

  String toJson() => json.encode(toMap());

  factory UserModel.fromJson(String source) =>
      UserModel.fromMap(json.decode(source));

  @override
  String toString() {
    return 'UserModel(id: $id, authId: $authId, name: $name, email: $email, phone: $phone, createdAt: $createdAt, contestsLiberated: $contestsLiberated, isActive: $isActive)';
  }
}
