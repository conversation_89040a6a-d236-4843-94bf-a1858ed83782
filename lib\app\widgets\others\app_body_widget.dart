import 'package:flutter/material.dart';

class AppBodyWidget extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final bool centerWidgets;
  final bool isScrollable;

  const AppBodyWidget({
    Key? key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.centerWidgets = false,
    this.isScrollable = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget getCenter({required Widget child}) {
      if (!centerWidgets) return child;
      return Center(child: child);
    }

    Widget getScrollable({required Widget child}) {
      if (!isScrollable) return child;
      return SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        child: child,
      );
    }

    return SizedBox(
      width: MediaQuery.sizeOf(context).width,
      height: MediaQuery.sizeOf(context).height,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 10,
          horizontal: 20,
        ),
        child: getCenter(
          child: getScrollable(
            child: Column(
              mainAxisAlignment: mainAxisAlignment,
              crossAxisAlignment: crossAxisAlignment,
              children: children,
            ),
          ),
        ),
      ),
    );
  }
}
