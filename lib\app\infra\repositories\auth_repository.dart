import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../helpers/user_helper.dart';
import '../models/defaults/response_model.dart';
import '../models/defaults/user_model.dart';

extension SupabaseFilterExtensions on PostgrestFilterBuilder {
  PostgrestFilterBuilder in_(
    String column,
    List<dynamic> values,
  ) {
    return filter(column, 'in', '(${values.join(',')})');
  }
}

class AuthRepository {
  final _supabase = Supabase.instance.client;

  Future<ResponseModel<UserModel>> getCurrentUser() async {
    try {
      final response = await _supabase.from('tb_users').select().match(
          {'auth_id': UserHelper.instance.authUser?.id ?? ''}).maybeSingle();

      return ResponseModel.success(UserModel.fromMap(response ?? {}));
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error(
        "Não foi possível buscar dados do usuário",
      );
    }
  }

  Future<ResponseModel<List<UserModel>>> getUsersByInterval(
    DateTime? startAt,
    DateTime? endAt,
  ) async {
    try {
      var query = _supabase.from('tb_users').select();
      if (startAt != null && endAt != null) {
        query = query.gte('created_at', startAt).lte('created_at', endAt);
      }

      var response = await query;

      return ResponseModel.success(
        List.from(response.map((map) => UserModel.fromMap(map))),
      );
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error(
        "Não foi possível buscar dados do usuário",
      );
    }
  }

  Future<List<UserModel>> fetchUsersWithContests({
    DateTime? startAt,
    DateTime? endAt,
  }) async {
    try {
      final contentsResponse = await Supabase.instance.client
          .from('tb_contests')
          .select('id, name, kiwify_product_id');

      final usersResponse = await Supabase.instance.client
          .from('tb_users')
          .select('id, name, email, phone, created_at');

      if (usersResponse.isEmpty) return [];

      // Lista de IDs de usuários
      final userIds = usersResponse.map((user) => user['id'] as int).toList();

      // Busca pagamentos em paralelo
      final paymentsFuture = Supabase.instance.client
          .from('tb_kiwify_payments')
          .select('email, product_id, subscription_status');

      // Busca concursos usando a lista de IDs de usuários
      final contestsFuture = Supabase.instance.client
          .from('tb_user_contests')
          .select('user_id, tb_contests(name)')
          .in_('user_id', userIds);

      // Aguarda as respostas em paralelo
      final responses = await Future.wait([paymentsFuture, contestsFuture]);

      final paymentsResponse = responses[0];
      final contestsResponse = responses[1];

      // Consolida os dados
      return (usersResponse as List).map((user) {
        final userEmail = user['email'];
        final userId = user['id'];

        // Concursos pagos
        final userPayments = (paymentsResponse as List)
            .where((payment) =>
                payment['email'] == userEmail &&
                payment['subscription_status'] == 'active')
            .map((payment) {
          return contentsResponse.firstWhere(
            (element) => element['kiwify_product_id'] == payment['product_id'],
          )['name'];
        }).toList();

        // Concursos manuais
        final userContests = (contestsResponse as List)
            .where((contest) => contest['user_id'] == userId)
            .map((contest) => contest['tb_contests']['name'])
            .toList();

        // Combina todos os concursos
        final contestsLiberated = {...userPayments, ...userContests}.toList();

        return UserModel(
          id: user['id'],
          name: user['name'] ?? "",
          email: userEmail,
          phone: user['phone'] ?? '',
          createdAt: DateTime.parse(user['created_at']),
          isActive: true,
          contestsLiberated: contestsLiberated,
        );
      }).toList();
    } catch (e) {
      debugPrint('Erro ao buscar usuários: $e');
      return [];
    }
  }

  Stream<List<Map<String, dynamic>>> getUsersAsStream({
    String emailToSearch = '',
    String nameToSearch = '',
  }) {
    var tb = _supabase.from('tb_users');
    var stream = tb.stream(primaryKey: ['id']);

    if (emailToSearch.isNotEmpty) {
      return tb
          .select()
          // .textSearch('email', "$emailToSearch:*")
          .or('email.ilike.$emailToSearch%')
          .order('name', ascending: true)
          .asStream();
    } else if (nameToSearch.isNotEmpty) {
      return tb
          .select()
          .or('name.ilike.$nameToSearch%')
          .order('name', ascending: true)
          .asStream();
    }

    return stream.limit(50).order('name', ascending: true);
  }

  Future<ResponseModel<bool>> updateIsActive(UserModel value) async {
    try {
      await _supabase.from('tb_users').update({
        'is_active': value.isActive,
      }).eq('id', value.id!);

      return ResponseModel.success(value.isActive);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error(
        "Não foi possível atualizar o status do usuário",
      );
    }
  }

  Future<ResponseModel<AuthResponse>> loginWithEmailAndPassword(
    String email,
    String password,
  ) async {
    try {
      return ResponseModel.success(
        await Supabase.instance.client.auth.signInWithPassword(
          email: email,
          password: password,
        ),
      );
    } catch (error) {
      if (error is AuthException &&
          error.message == 'Invalid login credentials') {
        return ResponseModel.error("E-mail ou senha incorretos");
      }
      debugPrint('ERRO: $error');
      return ResponseModel.error("Não foi possível fazer o login");
    }
  }

  Future<void> logoutUser() async => await _supabase.auth.signOut();
}
