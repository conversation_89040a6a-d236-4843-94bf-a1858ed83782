import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/contest_model.dart';
import '../../../app/infra/models/matter_model.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/confirmation_dialog.dart';
import '../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../app/widgets/design_system/text_default.dart';
import '../../../app/widgets/states/app_widget_empty.dart';
import '../../../app/widgets/states/app_widget_loading.dart';
import '../../admin_questions/admin_questions_page.dart';
import '../../widgets/info_item.dart';
import '../admin_contests_controller.dart';
import 'dialog_add_edit_matter.dart';

class DialogContestInfo extends StatefulWidget {
  const DialogContestInfo({super.key, required this.contest});

  final ContestModel contest;

  @override
  State<DialogContestInfo> createState() => _DialogContestInfoState();
}

class _DialogContestInfoState extends State<DialogContestInfo> {
  final controller = Get.find<AdminContestsController>();

  bool loading = false;
  List<MatterModel> matters = [];

  void getMatters() async {
    setState(() => loading = true);

    matters = await AdminContestsController.to.getMatters(
      contestId: widget.contest.id.toString(),
    );

    setState(() => loading = false);
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => getMatters());
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: 'Informações do concurso',
      height: MediaQuery.of(context).size.height * 0.75,
      form: Expanded(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                visible: widget.contest.image.isNotEmpty,
                child: Center(
                  child: Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      image: widget.contest.image.isNotEmpty
                          ? DecorationImage(
                              image: NetworkImage(
                                '${widget.contest.image}?t='
                                '${DateTime.now().millisecondsSinceEpoch}',
                              ),
                              fit: BoxFit.cover,
                            )
                          : const DecorationImage(
                              image: AssetImage(
                                'assets/images/logo_without_background.png',
                              ),
                            ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 10),
              InfoItem(data: 'Nome', value: widget.contest.name),
              const SizedBox(height: 10),
              InfoItem(
                data: 'Link de pagamento',
                value: widget.contest.paymentLink,
              ),
              const SizedBox(height: 10),
              InfoItem(
                data: 'Id do produto no kiwify',
                value: widget.contest.kiwifyProductId,
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 30),
                child: Divider(thickness: 0.5),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const TextDefault(
                    text: 'Matérias',
                    color: AppColors.cardPrimaryLight,
                    fontSize: 18,
                  ),
                  Container(
                    decoration: const BoxDecoration(
                      color: AppColors.primary,
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: () async {
                        final matter = await Get.dialog(
                          DialogAddEditMatter(
                            contestId: widget.contest.id!,
                          ),
                        );

                        if (matter != null) {
                          matters.add(matter as MatterModel);
                          setState(() {});
                        }
                      },
                      icon: const Icon(
                        Icons.add,
                        size: 25,
                        color: AppColors.backgroundLight,
                      ),
                    ),
                  )
                ],
              ),
              Builder(
                builder: (context) {
                  if (loading) {
                    return const Padding(
                      padding: EdgeInsets.only(top: 10),
                      child: AppWidgetLoading(),
                    );
                  }

                  if (matters.isEmpty) {
                    return const AppWidgetEmpty(
                      message: 'Não há matérias cadastradas nesse edital',
                      showImage: true,
                    );
                  }

                  matters.sort((a, b) => a.order.compareTo(b.order));

                  return Column(
                    children: matters.map((m) {
                      return MatterWidget(
                        matter: m,
                        pressEdit: () async {
                          final matter = await Get.dialog(
                            DialogAddEditMatter(
                              contestId: widget.contest.id!,
                              matter: m,
                            ),
                          );

                          if (matter != null) {
                            matters.removeWhere((item) => item.id == m.id);
                            matters.add(matter as MatterModel);
                            setState(() {});
                          }
                        },
                        pressDelete: () async {
                          Get.dialog(
                            ConfirmationDialog(
                              message: 'Tem certeza que deseja excluir essa '
                                  'matéria e todas as questões cadastradas nela?',
                              subtitle: 'Essa ação não poderá ser desfeita.',
                              onConfirm: () async {
                                final result = await controller.deleteMatter(
                                  matterId: m.id!,
                                );

                                if (result) {
                                  matters.remove(m);
                                  setState(() {});
                                }
                              },
                            ),
                          );
                        },
                        pressSeeQuestions: () {
                          Get.toNamed(
                            '${AdminQuestionsPage.route}?mid=${m.id}&'
                            'cname=${widget.contest.name}',
                          );
                        },
                      );
                    }).toList(),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class MatterWidget extends StatelessWidget {
  const MatterWidget({
    super.key,
    required this.matter,
    required this.pressDelete,
    required this.pressEdit,
    required this.pressSeeQuestions,
  });

  final MatterModel matter;
  final VoidCallback pressDelete;
  final VoidCallback pressEdit;
  final VoidCallback pressSeeQuestions;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 3),
      child: Card(
        color: AppColors.secondary,
        elevation: 20,
        child: SizedBox(
          width: double.maxFinite,
          child: Padding(
            padding: const EdgeInsets.all(10),
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: (matter.needPay
                              ? AppColors.cardPrimaryLight
                              : AppColors.primaryGreen)
                          .withOpacity(0.5),
                      width: 2.0,
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(6),
                    child: TextDefault(
                      text: matter.needPay ? 'Paga' : 'Grátis',
                      color: (matter.needPay
                              ? AppColors.cardPrimaryLight
                              : AppColors.primaryGreen)
                          .withOpacity(0.5),
                      fontSize: 12,
                    ),
                  ),
                ),
                const SizedBox(height: 10),
                TextDefault(
                  text: matter.name,
                ),
                const SizedBox(height: 10),
                Wrap(
                  alignment: WrapAlignment.center,
                  runAlignment: WrapAlignment.center,
                  spacing: 15,
                  runSpacing: 15,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: AppColors.primary,
                      ),
                      child: IconButton(
                        onPressed: pressEdit,
                        icon: const Icon(
                          Icons.edit,
                          color: AppColors.backgroundLight,
                        ),
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: AppColors.primary,
                      ),
                      child: IconButton(
                        onPressed: pressDelete,
                        icon: const Icon(
                          Icons.delete,
                          color: AppColors.backgroundLight,
                        ),
                      ),
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: AppColors.primary,
                      ),
                      child: IconButton(
                        onPressed: pressSeeQuestions,
                        icon: const TextDefault(
                          text: 'Ver questões',
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
