import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../themes/app_colors.dart';
import 'text_default.dart';

class AppTextFormField extends StatefulWidget {
  final String? title;
  final Widget? suffixIcon;
  final int maxLines;
  final String? hintText;
  final TextEditingController? controller;
  final bool readOnly;
  final bool enabled;
  final bool isPassword;
  final FocusNode? focusNode;
  final bool optional;
  final List<TextInputFormatter>? inputFormatters;
  final String? Function(String?)? validator;
  final TextInputType? keyboardType;
  final Function()? onTap;
  final Function(String)? onChanged;
  final Widget? prefixIcon;
  final double? radius;
  final Color? fillColor;
  final Color? textColor;
  final TextInputAction? textInputAction;
  final void Function(String)? onFieldSubmitted;

  const AppTextFormField({
    Key? key,
    this.title,
    this.suffixIcon,
    this.maxLines = 1,
    this.hintText,
    this.controller,
    this.readOnly = false,
    this.enabled = true,
    this.isPassword = false,
    this.focusNode,
    this.optional = false,
    this.inputFormatters,
    this.validator,
    this.keyboardType,
    this.onTap,
    this.onChanged,
    this.prefixIcon,
    this.radius,
    this.fillColor,
    this.textColor,
    this.textInputAction = TextInputAction.next,
    this.onFieldSubmitted,
  }) : super(key: key);

  @override
  State<AppTextFormField> createState() => _AppTextFormFieldState();
}

class _AppTextFormFieldState extends State<AppTextFormField> {
  bool obscureText = true;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.title != null)
          Padding(
            padding: const EdgeInsets.only(bottom: 2),
            child: TextDefault(
              text: widget.title ?? '',
              color: widget.textColor ?? Colors.white,
              fontWeight: FontWeight.w500,
            ),
          ),
        TextFormField(
          onFieldSubmitted: widget.onFieldSubmitted,
          textInputAction: widget.textInputAction,
          onChanged: widget.onChanged,
          keyboardType: widget.keyboardType,
          maxLines: widget.maxLines,
          focusNode: widget.focusNode,
          onTap: widget.onTap,
          readOnly: widget.readOnly,
          enabled: widget.enabled,
          controller: widget.controller,
          obscureText: !widget.isPassword ? false : obscureText,
          inputFormatters: widget.inputFormatters,
          validator: widget.validator ??
              (value) {
                if (widget.optional == false &&
                    (value?.trim().isEmpty ?? true)) {
                  return 'Campo obrigatório.';
                }
                return null;
              },
          decoration: InputDecoration(
            filled: true,
            fillColor: widget.fillColor ?? Colors.grey,
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
            suffixIcon: Visibility(
              visible: widget.isPassword,
              replacement: widget.suffixIcon ?? const SizedBox.shrink(),
              child: IconButton(
                icon: Icon(
                  obscureText
                      ? Icons.remove_red_eye
                      : Icons.remove_red_eye_outlined,
                  color: AppColors.backgroundBlack,
                ),
                onPressed: () {
                  obscureText = !obscureText;
                  setState(() {});
                },
              ),
            ),
            prefixIcon: widget.prefixIcon,
            hintText: widget.hintText,
            hintStyle: TextStyle(
              fontFamily: 'Roboto',
              color: widget.textColor ?? Colors.black,
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 16),
              borderSide: const BorderSide(
                color: Colors.white70,
                width: 1.4,
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 16),
              borderSide: const BorderSide(
                color: Colors.white70,
                width: 1.4,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(widget.radius ?? 16),
              borderSide: const BorderSide(
                color: Colors.white70,
                width: 1.4,
              ),
            ),
          ),
          style: TextStyle(
            fontFamily: 'Roboto',
            color: widget.textColor ?? Colors.black,
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
