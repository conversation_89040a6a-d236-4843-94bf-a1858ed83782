class ContestModel {
  final int? id;
  final String name;
  final String description;
  final DateTime createdAt;
  final bool needPay;
  final String paymentLink;
  final String kiwifyProductId;
  final int order;
  final String firstText;
  final String buttonText;
  String image;

  int? qtMatters;
  int? qtUsers;

  ContestModel({
    this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.image,
    required this.needPay,
    required this.paymentLink,
    required this.kiwifyProductId,
    required this.order,
    required this.firstText,
    required this.buttonText,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'image': image,
      'need_pay': needPay,
      'kiwify_product_id': kiwifyProductId,
      'payment_link': paymentLink,
      'order': order,
      'firstText': firstText,
      'buttonText': buttonText,
    };
  }

  factory ContestModel.fromMap(Map<String, dynamic> map) {
    return ContestModel(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      createdAt: DateTime.tryParse(map['created_at'] ?? '0') ?? DateTime.now(),
      image: map['image'] ?? '',
      needPay: map['need_pay'] ?? true,
      paymentLink: map['payment_link'] ?? '',
      kiwifyProductId: map['kiwify_product_id'] ?? '',
      order: map['order'] ?? 0,
      firstText: map['firstText'] ?? '',
      buttonText: map['buttonText'] ?? '',
    );
  }
}
