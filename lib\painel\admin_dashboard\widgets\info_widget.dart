import 'package:flutter/material.dart';

import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/app_card_widget.dart';
import '../../../app/widgets/design_system/text_default.dart';

class InfoWidget extends StatelessWidget {
  const InfoWidget({
    super.key,
    required this.icon,
    required this.data,
    required this.value,
  });

  final IconData icon;
  final String data;
  final int value;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 170,
      width: 200,
      child: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10),
            child: AppCardWidget(
              width: 190,
              height: 130,
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextDefault(
                      text: data,
                      fontSize: 18,
                    ),
                    TextDefault(
                      text: value.toString(),
                      fontSize: 18,
                    ),
                  ],
                ),
              ),
            ),
          ),
          Align(
            alignment: Alignment.topRight,
            child: CircleAvatar(
              backgroundColor: AppColors.primary,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Icon(
                  icon,
                  color: AppColors.backgroundLight,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
