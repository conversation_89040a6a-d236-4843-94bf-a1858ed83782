import 'package:flutter/material.dart';

import '../../app/themes/app_colors.dart';
import '../../app/widgets/design_system/text_default.dart';

class AdminButtonDefault extends StatelessWidget {
  final String title;
  final Color? color;
  final bool loading;

  final Function() onTap;

  const AdminButtonDefault({
    super.key,
    required this.title,
    this.color,
    this.loading = false,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 10,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: color ?? AppColors.primary,
        ),
        child: loading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : TextDefault(
                text: title,
                color: Colors.black,
              ),
      ),
    );
  }
}
