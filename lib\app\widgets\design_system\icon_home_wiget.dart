import 'package:flutter/material.dart';

class IconHomeWidget extends StatelessWidget {
  final Function()? onTap;
  final Widget icon;
  final Color? backgroundColor;

  const IconHomeWidget({
    super.key,
    this.onTap,
    required this.icon,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 23,
        backgroundColor: backgroundColor ?? Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: icon,
        ),
      ),
    );
  }
}
