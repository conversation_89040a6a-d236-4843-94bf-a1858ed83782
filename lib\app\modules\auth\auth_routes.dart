import 'package:get/get.dart';

import '../../infra/repositories/auth_repository.dart';
import 'login/login_controller.dart';
import 'login/login_page.dart';
import 'recover_password/recover_password_controller.dart';
import 'recover_password/recover_password_page.dart';
import 'recover_password/send_email_recover_password/send_email_recover_password_controller.dart';
import 'recover_password/send_email_recover_password/send_email_recover_password_page.dart';

final authRoutes = [
  GetPage(
    name: LoginPage.route,
    page: () => const LoginPage(),
    binding: BindingsBuilder.put(() => LoginController(AuthRepository())),
  ),
  GetPage(
    name: RecoverPasswordPage.route,
    page: () => const RecoverPasswordPage(),
    binding: BindingsBuilder.put(() => RecoverPasswordController()),
  ),
  GetPage(
    name: SendEmailRecoverPasswordPage.route,
    page: () => const SendEmailRecoverPasswordPage(),
    binding: BindingsBuilder.put(() => SendEmailRecoverPasswordController()),
  ),
];
