class AlternativeModel {
  final int? id;
  final int questionId;
  final String description;
  final String prefix;
  final DateTime createdAt;
  final bool correct;

  AlternativeModel({
    this.id,
    required this.questionId,
    required this.description,
    required this.prefix,
    required this.createdAt,
    required this.correct,
  });

  Map<String, dynamic> toMap() {
    return {
      'question_id': questionId,
      'description': description,
      'prefix': prefix,
      'created_at': createdAt.toIso8601String(),
      'correct': correct,
    };
  }

  factory AlternativeModel.fromMap(Map<String, dynamic> map) {
    return AlternativeModel(
      id: map['id'],
      description: map['description'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
      questionId: map['question_id'],
      prefix: map['prefix'] ?? '',
      correct: map['correct'] ?? false,
    );
  }
}
