import 'package:get/get.dart';

import '../../app/infra/repositories/contests_repository.dart';
import '../../app/infra/repositories/open_contest_repository.dart';
import 'admin_open_contests_controller.dart';
import 'admin_open_contests_page.dart';

final adminOpenContestsRoutes = [
  GetPage(
    name: AdminOpenContestsPage.route,
    page: () => const AdminOpenContestsPage(),
    binding: BindingsBuilder.put(() {
      Get.lazyPut(() => OpenContestsRepository());
      Get.lazyPut(() => ContestsRepository());
      return AdminOpenContestsController(Get.find(), Get.find());
    }),
  ),
];
