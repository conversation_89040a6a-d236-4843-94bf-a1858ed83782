import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppBarCustom extends StatelessWidget implements PreferredSize {
  const AppBarCustom({super.key});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Image.asset(
        'assets/images/logo_app_bar.png',
        width: Get.width * 0.85,
      ),
      toolbarHeight: 75,
      centerTitle: true,
      automaticallyImplyLeading: false,
    );
  }

  @override
  Widget get child => const AppBarCustom();

  @override
  Size get preferredSize => const Size(double.maxFinite, 75);
}
