import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../widgets/design_system/text_default.dart';
import '../../login/login_page.dart';

class ReturnLogin extends StatelessWidget {
  const ReturnLogin({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: TextButton(
        onPressed: () => Get.offNamed(LoginPage.route),
        child: const TextDefault(
          text: 'Voltar para o Login',
          fontWeight: FontWeight.w500,
          color: Colors.white,
        ),
      ),
    );
  }
}
