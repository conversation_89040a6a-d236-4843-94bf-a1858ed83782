import 'dart:convert';

class UnilinkModel {
  final String userId;
  final String? occurrenceId;
  final String? individualId;

  UnilinkModel({
    required this.userId,
    this.occurrenceId,
    this.individualId,
  });

  Map<String, dynamic> toMap() {
    final result = <String, dynamic>{};

    result.addAll({'userId': userId});
    if (occurrenceId != null) {
      result.addAll({'occurrenceId': occurrenceId});
    }
    if (individualId != null) {
      result.addAll({'individualId': individualId});
    }

    return result;
  }

  factory UnilinkModel.fromMap(Map<String, dynamic> map) {
    return UnilinkModel(
      userId: map['userId'] ?? '',
      occurrenceId: map['occurrenceId'],
      individualId: map['individualId'],
    );
  }

  String toJson() => json.encode(toMap());

  factory UnilinkModel.fromJson(String source) =>
      UnilinkModel.fromMap(json.decode(source));
}
