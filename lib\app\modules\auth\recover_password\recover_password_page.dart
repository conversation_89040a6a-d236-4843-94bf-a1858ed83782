import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../widgets/design_system/app_button_default.dart';
import '../../../widgets/design_system/app_text_form_field.dart';
import '../../../widgets/design_system/background_auth.dart';
import '../../../widgets/design_system/introduction_auth.dart';
import '../../../widgets/design_system/text_default.dart';
import 'recover_password_controller.dart';
import 'widgets/return_login.dart';

class RecoverPasswordPage extends StatelessWidget {
  const RecoverPasswordPage({super.key});

  static const route = "/recover-password";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            const BackgroundAuth(),
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Column(
                  children: [
                    const IntroductionAuth(
                      text: 'Recuperar senha',
                    ),
                    const TextDefault(
                      text: 'Informe seu e-mail para\ncontinuar',
                      fontSize: 16,
                      fontWeight: FontWeight.w300,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 30),
                    GetBuilder<RecoverPasswordController>(
                      builder: (control) {
                        return AppTextFormField(
                          hintText: 'Digite seu E-mail',
                          controller: control.emailController,
                        );
                      },
                    ),
                    const SizedBox(height: 30),
                    GetBuilder<RecoverPasswordController>(
                      builder: (control) {
                        return AppButtonDefault(
                          onTap: control.resetPassword,
                          text: 'Enviar',
                          isValid: true,
                          isLoading: control.isLoading,
                        );
                      },
                    ),
                    const ReturnLogin(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
