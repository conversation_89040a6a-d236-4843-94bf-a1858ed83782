List<String> getBodyPartsInPt(Map<String, dynamic> bodyParts) {
  List<String> list = [];

  bodyParts.forEach((key, value) {
    if (value == true) {
      int index = partsInEnglish.indexOf(key);
      list.add(partsInPt[index]);
    }
  });

  return list;
}

List<String> getBodyPartsInEnglish(List<String> parts) {
  List<String> list = [];

  for (String part in parts) {
    int index = partsInPt.indexOf(part);
    list.add(partsInEnglish[index]);
  }

  return list;
}

List<String> partsInEnglish = [
  'head',
  'neck',
  'leftShoulder',
  'leftUpperArm',
  'leftElbow',
  'leftLowerArm',
  'leftHand',
  'rightShoulder',
  'rightUpperArm',
  'rightElbow',
  'rightLowerArm',
  'rightHand',
  'upperBody',
  'lowerBody',
  'leftUpperLeg',
  'leftKnee',
  'leftLowerLeg',
  'leftFoot',
  'rightUpperLeg',
  'rightKnee',
  'rightLowerLeg',
  'rightFoot',
  'abdomen',
];

List<String> partsInPt = [
  'Cabeça',
  'Pescoço/Nuca',
  'Ombro esquerdo',
  'Braço esquerdo',
  'Cotovelo esquerdo',
  'Antebraço esquerdo',
  'Mão esquerda',
  'Ombro direito',
  'Braço direito',
  'Cotovelo direito',
  'Antebraço direito',
  'Mão direita',
  'Tórax',
  'Abdômen',
  'Coxa esquerda',
  'Joelho esquerdo',
  'Parte inferior esquerda da perna ("Canela")',
  'Pé esquerdo',
  'Coxa direita',
  'Joelho direito',
  'Parte inferior direita da perna ("Canela")',
  'Pé direito',
  'Pelve/Nádegas',
];
