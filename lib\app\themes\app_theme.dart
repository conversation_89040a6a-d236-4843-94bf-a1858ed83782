import 'package:flutter/material.dart';

import 'app_colors.dart';

class AppTheme {
  AppTheme._();

  static final light = ThemeData(
    useMaterial3: true,
    primaryColor: AppColors.primary,
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.secondary,
      titleTextStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w700,
      ),
      centerTitle: true,
    ),
    scaffoldBackgroundColor: AppColors.backgroundBlack,
    textTheme: const TextTheme(
      bodyMedium: TextStyle(color: AppColors.backgroundBlack),
    ),
    colorScheme: ColorScheme.fromSeed(seedColor: AppColors.primary),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.primary,
      selectedItemColor: AppColors.primary,
      unselectedLabelStyle: TextStyle(fontSize: 12),
      selectedLabelStyle: TextStyle(
        fontSize: 12.5,
        fontWeight: FontWeight.bold,
      ),
      unselectedIconTheme: IconThemeData(
        size: 25,
      ),
      selectedIconTheme: IconThemeData(
        color: AppColors.primary,
        size: 30,
      ),
    ),
  );
  static final dark = ThemeData(
    useMaterial3: true,
    brightness: Brightness.light,
    primaryColor: AppColors.primary,
    appBarTheme: const AppBarTheme(
      backgroundColor: AppColors.secondary,
      titleTextStyle: TextStyle(
        fontSize: 20,
        fontWeight: FontWeight.w700,
      ),
      centerTitle: true,
    ),
    scaffoldBackgroundColor: AppColors.backgroundBlack,
    textTheme: const TextTheme(
      bodyMedium: TextStyle(color: Colors.white),
    ),
    colorScheme: ColorScheme.fromSeed(seedColor: AppColors.primary),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      backgroundColor: AppColors.primary,
      selectedItemColor: AppColors.primary,
      unselectedLabelStyle: TextStyle(fontSize: 12),
      selectedLabelStyle: TextStyle(
        fontSize: 12.5,
        fontWeight: FontWeight.bold,
      ),
      unselectedIconTheme: IconThemeData(
        size: 25,
      ),
      selectedIconTheme: IconThemeData(
        color: AppColors.primary,
        size: 30,
      ),
    ),
  );
}
