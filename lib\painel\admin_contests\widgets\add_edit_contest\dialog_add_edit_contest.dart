import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../../app/infra/models/contest_model.dart';
import '../../../../app/themes/app_colors.dart';
import '../../../../app/widgets/design_system/app_button_default.dart';
import '../../../../app/widgets/design_system/app_text_form_field.dart';
import '../../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../../app/widgets/design_system/text_default.dart';
import '../../admin_contests_controller.dart';
import 'contest_image_widget.dart';

class DialogAddEditContest extends StatefulWidget {
  const DialogAddEditContest({super.key, this.contest});

  final ContestModel? contest;

  @override
  State<DialogAddEditContest> createState() => _DialogAddEditContestState();
}

class _DialogAddEditContestState extends State<DialogAddEditContest> {
  final _formKey = GlobalKey<FormState>();

  final controller = Get.find<AdminContestsController>();

  final contestNameController = TextEditingController();
  final paymentLinkController = TextEditingController();
  final productIdController = TextEditingController();
  final orderController = TextEditingController();
  final firstTextController = TextEditingController();
  final buttonTextController = TextEditingController();

  bool needPay = true;
  bool imageLoading = false;
  bool loading = false;

  PlatformFile? imageFile;
  String imageUrl = '';

  @override
  void initState() {
    super.initState();
    contestNameController.addListener(() => setState(() {}));
    paymentLinkController.addListener(() => setState(() {}));
    productIdController.addListener(() => setState(() {}));
    orderController.addListener(() => setState(() {}));
    buttonTextController.addListener(() => setState(() {}));
    firstTextController.addListener(() => setState(() {}));

    Future.microtask(() {
      if (widget.contest != null) {
        setState(() => imageLoading = true);

        contestNameController.text = widget.contest!.name;
        paymentLinkController.text = widget.contest!.paymentLink;
        productIdController.text = widget.contest!.kiwifyProductId;
        orderController.text = widget.contest!.order.toString();
        firstTextController.text = widget.contest!.firstText;
        buttonTextController.text = widget.contest!.buttonText;

        if (widget.contest!.image.isNotEmpty) {
          imageUrl = '${widget.contest!.image}?t='
              '${DateTime.now().millisecondsSinceEpoch}';
        }

        needPay = widget.contest!.needPay;

        setState(() => imageLoading = false);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: widget.contest != null ? 'Editar concurso' : 'Adicionar concurso',
      height: MediaQuery.of(context).size.height * 0.75,
      form: Expanded(
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(
                  child: ContestImageWidget(
                    isLoading: imageLoading,
                    isEdit: widget.contest != null,
                    imageFile: imageFile,
                    imageUrl: imageUrl,
                    onTap: () async {
                      final file = await controller.getFileImageGallery();

                      if (file != null) setState(() => imageFile = file);
                    },
                  ),
                ),
                Row(
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(left: 3),
                      child: TextDefault(
                        text: 'Precisa pagar',
                        fontSize: 14,
                        fontWeight: FontWeight.w800,
                        color: AppColors.backgroundLight,
                      ),
                    ),
                    Transform.scale(
                      scale: 0.7,
                      child: Switch(
                        activeColor: Colors.blueAccent,
                        value: needPay,
                        onChanged: (value) {
                          needPay = value;
                          setState(() {});
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Nome',
                  controller: contestNameController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Posição',
                  controller: orderController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                ),
                // const SizedBox(height: 10),
                // AppTextFormField(
                //   optional: true,
                //   title: 'Texto do botão',
                //   controller: buttonTextController,
                // ),
                Visibility(
                  visible: needPay,
                  child: Column(
                    children: [
                      const SizedBox(height: 10),
                      AppTextFormField(
                        optional: !needPay,
                        title: 'Link de pagamento',
                        controller: paymentLinkController,
                      ),
                      const SizedBox(height: 10),
                      AppTextFormField(
                        optional: !needPay,
                        title: 'Id do produto no kiwify',
                        controller: productIdController,
                      ),
                    ],
                  ),
                ),
                // const SizedBox(height: 20),
                // const Padding(
                //   padding: EdgeInsets.symmetric(horizontal: 40),
                //   child: Divider(),
                // ),
                // Padding(
                //   padding: const EdgeInsets.only(top: 20, bottom: 5),
                //   child: Row(
                //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                //     children: [
                //       const TextDefault(
                //         text: 'Primeiro texto ',
                //         color: Colors.white,
                //         fontWeight: FontWeight.w500,
                //       ),
                //       IconButton(
                //         onPressed: () => Get.dialog(
                //           const DialogInfoMarkdown(),
                //         ),
                //         icon: const Icon(
                //           Icons.info_outline,
                //           color: Colors.white,
                //         ),
                //       )
                //     ],
                //   ),
                // ),
                // AppTextFormField(
                //   optional: true,
                //   controller: firstTextController,
                //   maxLines: 6,
                // ),
                // const SizedBox(height: 5),
                // const TextDefault(
                //   text: 'Demonstração',
                //   color: Colors.white,
                //   fontWeight: FontWeight.w500,
                // ),
                // const SizedBox(height: 3),
                // Card(
                //   color: Colors.white70,
                //   child: Padding(
                //     padding: const EdgeInsets.all(10),
                //     child: Markdown(
                //       physics: const NeverScrollableScrollPhysics(),
                //       data: firstTextController.text,
                //       shrinkWrap: true,
                //       styleSheet: MarkdownStyleSheet(
                //         p: const TextStyle(
                //           fontFamily: 'Roboto',
                //           fontSize: 14,
                //           fontWeight: FontWeight.w400,
                //           color: Colors.black,
                //         ),
                //         strong: const TextStyle(
                //           fontFamily: 'Roboto',
                //           fontSize: 14,
                //           fontWeight: FontWeight.w700,
                //           color: Colors.black,
                //         ),
                //       ),
                //     ),
                //   ),
                // ),
                const SizedBox(height: 30),
                AppButtonDefault(
                  isLoading: loading,
                  isValid: _formKey.currentState?.validate(),
                  width: double.maxFinite,
                  onTap: () async {
                    setState(() => loading = true);

                    final contest = ContestModel(
                      id: widget.contest?.id,
                      name: contestNameController.text.trim(),
                      description: '',
                      createdAt: widget.contest?.createdAt ?? DateTime.now(),
                      image: widget.contest?.image ?? '',
                      needPay: needPay,
                      kiwifyProductId: productIdController.text.trim(),
                      paymentLink: paymentLinkController.text.trim(),
                      order: int.parse(orderController.text.trim()),
                      firstText: firstTextController.text.trim(),
                      buttonText: buttonTextController.text.trim(),
                    );

                    if (widget.contest != null) {
                      await controller.updateContest(
                        contest,
                        imageFile: imageFile,
                      );
                    } else {
                      await controller.createContest(
                        contest,
                        imageFile: imageFile,
                      );
                    }

                    setState(() => loading = false);
                  },
                  text: widget.contest != null
                      ? 'Salvar alterações'
                      : 'Adicionar',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
