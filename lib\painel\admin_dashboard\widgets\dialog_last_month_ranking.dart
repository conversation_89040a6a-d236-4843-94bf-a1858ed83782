import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/ranking/ranking_item_model.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../app/widgets/design_system/text_default.dart';
import '../../../app/widgets/states/app_widget_loading.dart';
import '../admin_dashboard_controller.dart';

class DialogLastMonthRanking extends StatefulWidget {
  const DialogLastMonthRanking({super.key});

  @override
  State<DialogLastMonthRanking> createState() => _DialogLastMonthRankingState();
}

class _DialogLastMonthRankingState extends State<DialogLastMonthRanking> {
  final controller = Get.find<AdminDashboardController>();
  List<RankingItemModel> items = [];
  bool loading = true;

  Color colorName(int position) {
    switch (position) {
      case 1:
        return AppColors.firstPlace;
      case 2:
        return AppColors.secondPlace;
      case 3:
        return AppColors.thirdPlace;
      default:
        return Colors.white;
    }
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      items = await controller.getLastMonthRankinget();
      setState(() => loading = false);
    });
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: 'Ranking do mês passado',
      form: Builder(builder: (context) {
        if (loading) return const Center(child: AppWidgetLoading());

        if (items.isEmpty) {
          return Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Expanded(
                child: TextDefault(
                  text: 'Nenhum resultado encontrado',
                ),
              ),
              Image.asset(
                'assets/images/ranking_empty.png',
                height: 60,
              ),
            ],
          );
        }
        return Column(
          children: items.map((i) {
            if (i.scoreValue == 0) return const SizedBox();

            return Row(
              children: [
                Expanded(
                  child: TextDefault(
                    text: '${i.position}° ${i.userName}',
                    color: colorName(i.position ?? 0),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 10),
                TextDefault(
                  text: i.scoreValue.toString(),
                  textAlign: TextAlign.end,
                ),
              ],
            );
          }).toList(),
        );
      }),
    );
  }
}
