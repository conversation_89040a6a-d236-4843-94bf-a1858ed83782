import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/alternative_model.dart';
import '../models/defaults/response_model.dart';
import '../models/question_model.dart';

class QuestionsRepository {
  final _supabaseQuestions = Supabase.instance.client.from('tb_questions');
  final _supabaseAlternatives =
      Supabase.instance.client.from('tb_alternatives');

  Future<ResponseModel<List<QuestionModel>>> getQuestions({
    required String matterId,
  }) async {
    try {
      List<QuestionModel> list = [];

      final data = await _supabaseQuestions
          .select()
          .eq('matter_id', matterId)
          .order('number');

      for (var doc in data) {
        list.add(QuestionModel.fromMap(doc));
      }

      return ResponseModel.success(list);
    } catch (e) {
      debugPrint('Erro $e');
      return ResponseModel.error('Não foi possível buscar as questões');
    }
  }

  Future<ResponseModel<List<AlternativeModel>>> getAlternativesByQuestion({
    required String questionId,
  }) async {
    try {
      List<AlternativeModel> list = [];

      final data =
          await _supabaseAlternatives.select().eq('question_id', questionId);

      for (var doc in data) {
        list.add(AlternativeModel.fromMap(doc));
      }

      return ResponseModel.success(list);
    } catch (e) {
      debugPrint('Erro $e');
      return ResponseModel.error('Não foi possível buscar as questões');
    }
  }

  Future<ResponseModel<List<AlternativeModel>>> getAlternatives({
    required List<int> questionsIds,
  }) async {
    try {
      List<AlternativeModel> alts = [];

      for (var questionId in questionsIds) {
        final data = await _supabaseAlternatives.select().eq(
              'question_id',
              questionId,
            );

        for (var doc in data) {
          alts.add(AlternativeModel.fromMap(doc));
        }
      }

      return ResponseModel.success(alts);
    } catch (e) {
      debugPrint('Erro $e');
      return ResponseModel.error('Não foi possível buscar as alternativas');
    }
  }

  Future<ResponseModel<bool>> updateAlternative({
    required AlternativeModel alternative,
  }) async {
    try {
      await _supabaseAlternatives.update(alternative.toMap()).eq(
            'id',
            alternative.id!,
          );

      return ResponseModel.success(true);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao atualizar questão');
    }
  }

  Future<ResponseModel<QuestionModel>> createQuestion({
    required QuestionModel question,
  }) async {
    try {
      final oldData = await _supabaseQuestions.select().match({
        'matter_id': question.matterId,
        'number': question.number,
      });

      if (oldData.isNotEmpty) {
        return ResponseModel.warning(
          'Já exite uma questão com o número ${question.number} registrada'
          'nessa matéria',
        );
      }

      final response =
          await _supabaseQuestions.insert(question.toMap()).select().single();

      return ResponseModel.success(QuestionModel.fromMap(response));
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao registrar questão');
    }
  }

  Future<ResponseModel<bool>> updateQuestion({
    required QuestionModel question,
  }) async {
    try {
      await _supabaseQuestions.update(question.toMap()).eq('id', question.id!);

      return ResponseModel.success(true);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao atualizar questão');
    }
  }

  Future<ResponseModel<bool>> deleteQuestion({required int questionId}) async {
    try {
      await _supabaseQuestions.delete().eq('id', questionId);

      return ResponseModel.success(true);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao excluir questão');
    }
  }

  Future<ResponseModel<AlternativeModel>> createAlternative({
    required AlternativeModel alternative,
  }) async {
    try {
      final oldData = await _supabaseAlternatives.select().match({
        'prefix': alternative.prefix,
        'question_id': alternative.questionId,
      });

      if (oldData.isNotEmpty) {
        return ResponseModel.warning(
          'A alternativa ${alternative.prefix} já existe',
        );
      }

      final response = await _supabaseAlternatives
          .insert(alternative.toMap())
          .select()
          .single();

      return ResponseModel.success(AlternativeModel.fromMap(response));
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao criar alternativa');
    }
  }
}
