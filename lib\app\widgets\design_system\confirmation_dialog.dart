// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../themes/app_colors.dart';
import '../responsive/is_mobile.dart';
import 'text_default.dart';

class ConfirmationDialog extends StatelessWidget {
  const ConfirmationDialog({
    Key? key,
    this.message,
    this.subtitle,
    this.autoClose = true,
    required this.onConfirm,
  }) : super(key: key);

  final String? message;
  final String? subtitle;
  final bool autoClose;
  final VoidCallback onConfirm;

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppColors.secondary,
      child: SizedBox(
        width: isMobile(context)
            ? MediaQuery.of(context).size.width * 0.8
            : MediaQuery.of(context).size.width * 0.3,
        child: Padding(
          padding: const EdgeInsets.all(30),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              TextDefault(
                text: message ?? 'Tem certeza?',
                fontSize: 18,
                color: Colors.white,
              ),
              SizedBox(height: subtitle != null ? 8 : 20),
              if (subtitle != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: 30),
                  child: TextDefault(
                    text: subtitle ?? '',
                    fontSize: 14,
                    color: Colors.white,
                  ),
                ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Card(
                    color: AppColors.primaryRed,
                    child: TextButton(
                      onPressed: () {
                        onConfirm();
                        if (autoClose) {
                          Get.back(result: true);
                        }
                      },
                      child: const Padding(
                        padding: EdgeInsets.all(8),
                        child: TextDefault(
                          text: 'Sim',
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  Card(
                    elevation: 10,
                    color: AppColors.primary,
                    child: TextButton(
                      onPressed: Get.back,
                      child: const Padding(
                        padding: EdgeInsets.all(8),
                        child: TextDefault(
                          text: 'Não',
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
