import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../infra/helpers/user_helper.dart';
import '../../modules/auth/login/login_page.dart';

class AuthMiddleware extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    final helper = UserHelper.instance;
    if (helper.authUser == null ||
        helper.authUser?.email != '<EMAIL>') {
      return const RouteSettings(name: LoginPage.route);
    }
    return null;
  }
}
