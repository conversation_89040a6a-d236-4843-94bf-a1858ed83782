import 'package:get/get.dart';

import '../../app/infra/repositories/matters_repository.dart';
import '../../app/infra/repositories/questions_repository.dart';
import '../../app/shared/middlewares/auth_middleware.dart';
import 'admin_questions_controller.dart';
import 'admin_questions_page.dart';

List<GetPage> adminQuestionsRoutes = [
  GetPage(
    name: AdminQuestionsPage.route,
    page: () => const AdminQuestionsPage(),
    binding: BindingsBuilder(() {
      Get.lazyPut(() => QuestionsRepository());
      Get.lazyPut(() => MattersRepository());

      Get.put(AdminQuestionsController(Get.find(), Get.find()));
    }),
    middlewares: [AuthMiddleware()],
  ),
];
