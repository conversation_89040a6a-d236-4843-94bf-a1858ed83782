import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/infra/models/defaults/user_model.dart';
import '../../app/widgets/design_system/app_button_default.dart';
import '../../app/widgets/design_system/app_text_form_field.dart';
import '../../app/widgets/states/app_widget_empty.dart';
import '../../app/widgets/states/app_widget_error.dart';
import '../../app/widgets/states/app_widget_loading.dart';
import '../widgets/admin_responsive_body.dart';
import 'admin_users_controller.dart';
import 'widgets/user_widget.dart';

class AdminUsersPage extends StatelessWidget {
  const AdminUsersPage({super.key});

  static const route = '/usuarios';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AdminResponsiveBody(
        title: '<PERSON>uários',
        children: [
          GetBuilder<AdminUsersController>(
            builder: (control) {
              return Column(
                children: [
                  AppTextFormField(
                    hintText: "Pesquisar por nome",
                    controller: control.nameController,
                  ),
                  const SizedBox(height: 5),
                  AppTextFormField(
                    hintText: "Pesquisar por email",
                    controller: control.emailController,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 6),
          GetBuilder<AdminUsersController>(
            builder: (control) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  AppButtonDefault(
                    text: "Exportar dados para excel",
                    onTap: () async {
                      control.exportToExcel();
                    },
                    isValid: true,
                    isLoading: control.isLoading,
                    width: 300,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 6),
          GetBuilder<AdminUsersController>(
            builder: (control) {
              return StreamBuilder(
                key: Key(
                    "${control.nameController.text}:${control.emailController.text}"),
                stream: control.getUsersAsStream(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const AppWidgetLoading();
                  } else if (snapshot.hasError) {
                    return const AppWidgetError(
                      message: 'Erro ao trazer os usuários',
                      showImage: true,
                    );
                  } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const AppWidgetEmpty(
                      message: 'Nenhum usuário encontrado',
                      showImage: true,
                    );
                  }
                  final users = snapshot.data!;

                  return Column(
                    children: users.map((u) {
                      final user = UserModel.fromMap(u);
                      if (user.email == '<EMAIL>') {
                        return const SizedBox();
                      }

                      return UserWidget(
                        user: user,
                        onIsActive: (value) =>
                            control.updateIsActive(user, user.id!),
                      );
                    }).toList(),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}
