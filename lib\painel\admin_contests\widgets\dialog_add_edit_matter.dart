import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/matter_model.dart';
import '../../../app/shared/utils/app_snackbar.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/app_button_default.dart';
import '../../../app/widgets/design_system/app_text_form_field.dart';
import '../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../app/widgets/design_system/text_default.dart';
import '../admin_contests_controller.dart';

class DialogAddEditMatter extends StatefulWidget {
  const DialogAddEditMatter({
    super.key,
    this.matter,
    required this.contestId,
  });

  final MatterModel? matter;
  final int contestId;

  @override
  State<DialogAddEditMatter> createState() => _DialogAddEditMatterState();
}

class _DialogAddEditMatterState extends State<DialogAddEditMatter> {
  final _formKey = GlobalKey<FormState>();

  final controller = Get.find<AdminContestsController>();

  final matterNameController = TextEditingController();
  final orderController = TextEditingController();

  bool needPay = true;

  @override
  void initState() {
    super.initState();

    matterNameController.addListener(() => setState(() {}));
    orderController.addListener(() => setState(() {}));

    Future.microtask(() {
      if (widget.matter != null) {
        matterNameController.text = widget.matter!.name;
        orderController.text = widget.matter!.order.toString();
        needPay = widget.matter!.needPay;
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    matterNameController.dispose();
    orderController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: widget.matter != null ? 'Editar matéria' : 'Adicionar matéria',
      form: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          children: [
            Row(
              children: [
                const Padding(
                  padding: EdgeInsets.only(left: 3),
                  child: TextDefault(
                    text: 'Precisa pagar',
                    fontSize: 14,
                    fontWeight: FontWeight.w800,
                    color: AppColors.backgroundLight,
                  ),
                ),
                Transform.scale(
                  scale: 0.7,
                  child: Switch(
                    activeColor: Colors.blueAccent,
                    value: needPay,
                    onChanged: (value) {
                      setState(() => needPay = value);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            AppTextFormField(
              title: 'Nome',
              controller: matterNameController,
            ),
            const SizedBox(height: 10),
            AppTextFormField(
              title: 'Posição',
              controller: orderController,
              keyboardType: TextInputType.number,
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(4),
              ],
            ),
            const SizedBox(height: 30),
            AppButtonDefault(
              isLoading: controller.isLoading,
              isValid: _formKey.currentState?.validate(),
              width: double.maxFinite,
              onTap: () async {
                setState(() {});

                final matter = MatterModel(
                  id: widget.matter?.id,
                  name: matterNameController.text.trim(),
                  description: '',
                  createdAt: widget.matter?.createdAt ?? DateTime.now(),
                  contestId: widget.contestId,
                  order: int.parse(orderController.text),
                  needPay: needPay,
                );

                MatterModel? result;

                if (widget.matter != null) {
                  result = await controller.updateMatter(matter);
                  Get.back(result: result);

                  if (result == null) {
                    AppSnackbar.to.show(
                      'Não foi possível atualizar os dados da matéria',
                    );
                  }
                } else {
                  result = await controller.createMatter(matter);
                  Get.back(result: result);

                  if (result == null) {
                    AppSnackbar.to.show(
                      'Não foi possível adicionar a matéria',
                    );
                  }
                }
              },
              text: widget.matter != null ? 'Salvar alterações' : 'Adicionar',
            ),
          ],
        ),
      ),
    );
  }
}
