import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/widgets/design_system/app_button_default.dart';
import '../widgets/admin_responsive_body.dart';
import 'admin_dashboard_controller.dart';
import 'widgets/dialog_last_month_ranking.dart';
import 'widgets/info_widget.dart';
import 'widgets/table_contests_info.dart';

class AdminDashboardPage extends StatelessWidget {
  const AdminDashboardPage({super.key});

  static const route = '/dashboard';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AdminResponsiveBody(
        title: 'Dashboard',
        children: [
          GetBuilder<AdminDashboardController>(
            builder: (control) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppButtonDefault(
                    isValid: true,
                    width: 235,
                    onTap: () => Get.dialog(const DialogLastMonthRanking()),
                    text: 'Ver ranking do mês passado',
                  ),
                  const SizedBox(height: 20),
                  Align(
                    alignment: MediaQuery.of(context).size.width < 480
                        ? Alignment.center
                        : Alignment.topLeft,
                    child: Wrap(
                      spacing: 20,
                      children: [
                        InfoWidget(
                          data: 'Usuários\nsem plano',
                          icon: Icons.people,
                          value: control.usersInfo?.usersWithoutPlan ?? 0,
                        ),
                        InfoWidget(
                          data: 'Usuários\ncom plano',
                          icon: Icons.people,
                          value: control.usersInfo?.usersWithPlan ?? 0,
                        ),
                        InfoWidget(
                          data: 'Matérias',
                          icon: Icons.library_books,
                          value: control.matters,
                        ),
                        InfoWidget(
                          data: 'Questões',
                          icon: Icons.library_books,
                          value: control.questions,
                        ),
                      ],
                    ),
                  ),
                  const TableContestsInfo(),
                ],
              );
            },
          )
        ],
      ),
    );
  }
}
