import 'package:flutter/material.dart';

class AppAddIcon extends StatelessWidget {
  final Function()? onTap;

  const AppAddIcon({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: const CircleAvatar(
        radius: 30,
        backgroundColor: Colors.white,
        child: Padding(
          padding: EdgeInsets.all(8.0),
          child: Icon(
            Icons.add,
            size: 45,
          ),
        ),
      ),
    );
  }
}
