import 'package:flutter/material.dart';

import '../../widgets/design_system/background_auth.dart';
import '../../widgets/design_system/text_default.dart';

class SuccessPage extends StatelessWidget {
  const SuccessPage({super.key});

  static const route = '/successo';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Safe<PERSON>rea(
        child: Stack(
          children: [
            const BackgroundAuth(),
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Center(
                      child: Image.asset(
                        'assets/images/check_password_recovered.png',
                      ),
                    ),
                    const SizedBox(height: 20),
                    const TextDefault(
                      text: 'Operação realizada com sucesso!',
                      color: Colors.white,
                      fontWeight: FontWeight.w800,
                      textAlign: TextAlign.center,
                      fontSize: 18,
                    ),
                    const SizedBox(height: 10),
                    const TextDefault(
                      text: 'Você já pode retornar ao app e fazer login '
                          'com sua nova senha.',
                      color: Colors.white,
                      fontWeight: FontWeight.w500,
                      textAlign: TextAlign.center,
                      fontSize: 16,
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
