import 'package:get/get.dart';

import '../../app/infra/repositories/dashboard_repository.dart';
import '../../app/shared/middlewares/auth_middleware.dart';
import 'admin_dashboard_controller.dart';
import 'admin_dashboard_page.dart';

List<GetPage> adminDashboardRoutes = [
  GetPage(
    name: AdminDashboardPage.route,
    page: () => const AdminDashboardPage(),
    binding: BindingsBuilder(() {
      Get.lazyPut(() => DashboardRepository());
      Get.put(AdminDashboardController(Get.find()));
    }),
    middlewares: [AuthMiddleware()],
  ),
];
