import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/themes/app_colors.dart';
import 'add_edit_contest/dialog_add_edit_contest.dart';

class AddContestButton extends StatelessWidget {
  const AddContestButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 30, right: 30),
      child: Container(
        decoration: const BoxDecoration(
          color: AppColors.primary,
          shape: BoxShape.circle,
        ),
        child: IconButton(
          onPressed: () => Get.dialog(const DialogAddEditContest()),
          icon: const Icon(
            Icons.add,
            size: 35,
            color: AppColors.backgroundLight,
          ),
        ),
      ),
    );
  }
}
