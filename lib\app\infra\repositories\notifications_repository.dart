import 'dart:developer';

import 'package:painel_dos_aprovados/app/infra/models/notification_model.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/defaults/response_model.dart';

class NotificationsRepository {
  final _supabase = Supabase.instance.client.from('tb_notifications');

  Future<ResponseModel<NotificationModel>> createNotification({
    required String title,
    required String body,
  }) async {
    try {
      var res = await _supabase
          .insert({
            'title': title,
            'body': body,
            'topic': 'geral',
          })
          .select()
          .single();
      return ResponseModel.success(
        NotificationModel.fromMap(res),
      );
    } catch (e) {
      log('Erro ao criar notificação: $e');
      return ResponseModel.error('Não foi possível criar a notificação');
    }
  }

  Future<ResponseModel<List<NotificationModel>>> getNotifications() async {
    try {
      var res = await _supabase.select().order('created_at', ascending: false);

      return ResponseModel.success(
        res.map((e) => NotificationModel.fromMap(e)).toList(),
      );
    } catch (e) {
      log('Erro ao buscar notificações: $e');
      return ResponseModel.error('Não foi possível buscar as notificações');
    }
  }

  // delete notification
  Future<ResponseModel<void>> deleteNotification(int id) async {
    try {
      await _supabase.delete().eq('id', id);
      return ResponseModel.success(null);
    } catch (e) {
      log('Erro ao deletar notificação: $e');
      return ResponseModel.error('Não foi possível deletar a notificação');
    }
  }

  Future<ResponseModel> sendNotification({
    int? userId,
    String? topic,
    int? orderId,
    required String title,
    required String body,
  }) async {
    try {
      if (userId != null && (topic?.isNotEmpty ?? false)) {
        log("Informe o userId ou o topic. Os dois juntos não são permitidos!");
        return ResponseModel.error(
          "Informe o userId ou o topic. Os dois juntos não são permitidos!",
        );
      }

      var res = await Supabase.instance.client.functions.invoke(
        'send-notification',
        body: {
          if (userId != null) "userId": userId,
          if (topic?.isNotEmpty ?? false) "topic": topic,
          "title": title,
          "body": body,
          if (orderId != null) "data": {"orderId": orderId.toString()}
        },
      );

      log('Notification Status: $res}');
      return ResponseModel.success(null);
    } catch (error) {
      log("Não foi possível enviar notificação: $error");
      return ResponseModel.error('Não foi possível enviar a notificação');
    }
  }
}
