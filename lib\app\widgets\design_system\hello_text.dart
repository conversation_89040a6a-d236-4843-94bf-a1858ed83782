import 'package:flutter/material.dart';

import '../../infra/helpers/user_helper.dart';
import '../../infra/models/defaults/user_model.dart';
import '../../themes/app_colors.dart';
import 'text_default.dart';

class HelloText extends StatefulWidget {
  final Color? color;
  const HelloText({super.key, this.color});

  @override
  State<HelloText> createState() => _HelloTextState();
}

class _HelloTextState extends State<HelloText> {
  bool loading = false;
  UserModel? currentUser;

  void getUser() async {
    loading = true;
    setState(() {});
    currentUser = await UserHelper.instance.user();
    loading = false;
    setState(() {});
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => getUser());
  }

  @override
  Widget build(BuildContext context) {
    if (loading) {
      return Center(
        child: CircularProgressIndicator.adaptive(
          strokeWidth: 3.5,
          backgroundColor: widget.color ?? AppColors.backgroundLight,
          valueColor: AlwaysStoppedAnimation(widget.color ?? Colors.white),
        ),
      );
    }
    return TextDefault(
      text: 'Olá, '
          '${currentUser?.name.split(' ').first ?? 'candidato'}!',
      fontSize: 20,
      fontWeight: FontWeight.w600,
      color: widget.color ?? AppColors.backgroundLight,
    );
  }
}
