class QuestionModel {
  final int? id;
  final String name;
  final String? comment;
  final DateTime createdAt;
  final int matterId;
  final String board;
  final int? year;
  final int number;
  final String? cityOrState;

  QuestionModel({
    this.id,
    required this.name,
    required this.matterId,
    required this.createdAt,
    required this.board,
    required this.year,
    required this.comment,
    required this.number,
    required this.cityOrState,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'matter_id': matterId,
      'comment': comment,
      'created_at': createdAt.toIso8601String(),
      'board': board,
      'year': year,
      'number': number,
      'cityOrState': cityOrState,
    };
  }

  factory QuestionModel.fromMap(Map<String, dynamic> map) {
    return QuestionModel(
      id: map['id'] ?? 0,
      name: map['name'] ?? '',
      comment: map['comment'] ?? '',
      matterId: map['matter_id'] ?? 0,
      createdAt: DateTime.parse(map['created_at']),
      year: map['year'],
      board: map['board'] ?? '',
      number: map['number'] ?? map['id'],
      cityOrState: map['cityOrState'],
    );
  }
}
