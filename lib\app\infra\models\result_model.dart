import 'alternative_model.dart';

class ResultModel {
  int? id;
  final int userId;
  final int questionId;
  final int matterId;
  final int alternativeId;
  final DateTime createdAt;

  AlternativeModel? alternative;

  ResultModel({
    this.id,
    required this.userId,
    required this.questionId,
    required this.matterId,
    required this.alternativeId,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'questionId': questionId,
      'matterId': matterId,
      'alternativeId': alternativeId,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory ResultModel.fromMap(Map<String, dynamic> map) {
    return ResultModel(
      id: map['id'] ?? 0,
      userId: map['userId'] ?? 0,
      questionId: map['questionId'] ?? 0,
      matterId: map['matterId'] ?? 0,
      alternativeId: map['alternativeId'] ?? 0,
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}
