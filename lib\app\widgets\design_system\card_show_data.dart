import 'package:flutter/material.dart';

import 'text_default.dart';

class CardShowData extends StatelessWidget {
  final String text;
  final double? height;
  final double? width;
  final TextAlign? textAlign;

  const CardShowData({
    super.key,
    required this.text,
    this.height,
    this.textAlign,
    this.width,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width ?? 350,
      margin: EdgeInsets.zero,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: const RadialGradient(
          colors: [
            Color(0xff292929),
            Colors.black,
          ],
          center: Alignment.center,
          radius: 0.8,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 26),
        child: TextDefault(
          text: text,
          fontWeight: FontWeight.w500,
          textAlign: textAlign,
        ),
      ),
    );
  }
}
