import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'text_default.dart';

class AppBarWidget extends StatelessWidget implements PreferredSizeWidget {
  final String text;
  final void Function()? pressBack;

  const AppBarWidget({super.key, required this.text, this.pressBack});

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: GestureDetector(
        onTap: () {
          if (pressBack != null) {
            pressBack!();
          } else {
            Get.back();
          }
        },
        child: const Icon(
          Icons.arrow_back_ios_new,
        ),
      ),
      title: TextDefault(
        text: text,
        fontSize: 20,
        fontWeight: FontWeight.w500,
        color: Colors.white,
      ),
      centerTitle: true,
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
