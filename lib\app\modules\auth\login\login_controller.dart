import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../painel/admin_dashboard/admin_dashboard_page.dart';
import '../../../infra/helpers/user_helper.dart';
import '../../../infra/repositories/auth_repository.dart';
import '../../../shared/mixins/loader_manager.dart';
import '../../../shared/utils/app_snackbar.dart';

class LoginController extends GetxController with LoaderManagerMixin {
  final AuthRepository _authRepository;
  LoginController(this._authRepository);

  final emailController = TextEditingController();
  final passwordController = TextEditingController();

  @override
  void onReady() {
    emailController.addListener(update);
    passwordController.addListener(update);
    super.onReady();
  }

  @override
  void onClose() {
    emailController.dispose;
    passwordController.dispose;
    super.onClose();
  }

  Future<void> loginWithEmailPassword() async {
    changeLoading(true);

    final response = await _authRepository.loginWithEmailAndPassword(
      emailController.text.trim(),
      passwordController.text.trim(),
    );
    if (response.success) {
      var userResponse = await _authRepository.getCurrentUser();
      UserHelper.instance.user(userResponse.data);

      if (userResponse.data != null) {
        if (userResponse.data!.email != '<EMAIL>') {
          AppSnackbar.to.show('Permitido apenas para administradores');
          await _authRepository.logoutUser();
        } else {
          Get.offNamed(AdminDashboardPage.route);
        }
      } else {
        AppSnackbar.to.show(userResponse.message);
      }
    } else {
      AppSnackbar.to.show(response.message);
    }
    changeLoading(false);
  }

  bool get isFormValid {
    return GetUtils.isEmail(emailController.text.trim()) &&
        passwordController.text.isNotEmpty;
  }

  void clearFields() {
    emailController.clear();
    passwordController.clear();
  }
}
