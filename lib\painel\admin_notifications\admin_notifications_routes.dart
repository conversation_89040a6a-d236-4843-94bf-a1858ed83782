import 'package:get/get.dart';
import 'package:painel_dos_aprovados/painel/admin_notifications/admin_notifications_controller.dart';

import '../../app/infra/repositories/notifications_repository.dart';
import 'admin_notifications_page.dart';

List<GetPage> adminNotificationsRoutes = [
  GetPage(
    name: AdminNotificationsPage.route,
    page: () => const AdminNotificationsPage(),
    binding: BindingsBuilder(() {
      Get.lazyPut(() => NotificationsRepository());
      Get.put(AdminNotificationsController(Get.find()));
    }),
    transition: Transition.fadeIn,
  ),
];
