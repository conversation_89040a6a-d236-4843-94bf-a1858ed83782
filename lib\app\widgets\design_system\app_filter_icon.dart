import 'package:flutter/material.dart';

class AppFilterIcon extends StatelessWidget {
  final Function()? onTap;

  const AppFilterIcon({super.key, this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: CircleAvatar(
        radius: 23,
        backgroundColor: Colors.white,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Image.asset(
            'assets/icons/filter.png',
          ),
        ),
      ),
    );
  }
}
