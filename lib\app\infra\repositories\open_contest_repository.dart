import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/contest_model.dart';
import '../models/defaults/response_model.dart';
import '../models/open_contest_model.dart';

class OpenContestsRepository {
  final _supabase = Supabase.instance.client.from('tb_open_contests');
  final _contestsSupabase = Supabase.instance.client.from('tb_contests');

  Future<ResponseModel<OpenContestModel>> createOpenContest(OpenContestModel openContest) async {
    try {
      final data = await _supabase.insert(openContest.toMap()).select();
      return ResponseModel.success(OpenContestModel.fromMap(data.first));
    } catch (e) {
      debugPrint('Erro ao criar edital aberto: $e');
      return ResponseModel.error('Não foi possível criar o edital aberto');
    }
  }

  Future<ResponseModel<OpenContestModel>> updateOpenContest(OpenContestModel openContest) async {
    try {
      final data = await _supabase.update(openContest.toMap()).eq('id', openContest.id!).select();
      return ResponseModel.success(OpenContestModel.fromMap(data.first));
    } catch (e) {
      debugPrint('Erro ao atualizar edital aberto: $e');
      return ResponseModel.error('Não foi possível atualizar o edital aberto');
    }
  }

  Future<ResponseModel> deleteOpenContest(int id) async {
    try {
      await _supabase.delete().eq('id', id);
      return ResponseModel.success(null);
    } catch (e) {
      debugPrint('Erro ao deletar edital aberto: $e');
      return ResponseModel.error('Não foi possível deletar o edital aberto');
    }
  }

  Future<ResponseModel<List<OpenContestModel>>> getOpenContests() async {
    try {
      final data = await _supabase.select('*').order('createdAt', ascending: false);

      return ResponseModel.success(data.map((e) => OpenContestModel.fromMap(e)).toList());
    } catch (e) {
      debugPrint('Erro ao buscar concursos abertos: $e');
      return ResponseModel.error('Não foi possível buscar os concursos abertos');
    }
  }

  Future<ResponseModel<List<ContestModel>>> getContestCategories() async {
    try {
      final data = await _contestsSupabase
          .select('id, name, image, description')
          .order('order', ascending: true);

      return ResponseModel.success(data.map((e) => ContestModel.fromMap(e)).toList());
    } catch (e) {
      debugPrint('Erro ao buscar categorias de concursos: $e');
      return ResponseModel.error('Não foi possível buscar as categorias de concursos');
    }
  }

  Future<ResponseModel<List<OpenContestModel>>> getOpenContestsByCategory(int contestId) async {
    try {
      final data = await _supabase
          .select('*')
          .eq('contestId', contestId)
          .order('createdAt', ascending: false);

      return ResponseModel.success(data.map((e) => OpenContestModel.fromMap(e)).toList());
    } catch (e) {
      debugPrint('Erro ao buscar concursos por categoria: $e');
      return ResponseModel.error('Não foi possível buscar os concursos desta categoria');
    }
  }
}
