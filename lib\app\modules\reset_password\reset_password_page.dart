import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../shared/utils/app_snackbar.dart';
import '../../shared/utils/ternary_responsive_web.dart';
import '../../themes/app_colors.dart';
import '../../widgets/design_system/app_button_default.dart';
import '../../widgets/design_system/app_card_widget.dart';
import '../../widgets/design_system/app_text_form_field.dart';
import '../../widgets/design_system/background_auth.dart';
import '../../widgets/design_system/text_default.dart';
import 'success_page.dart';

class ResetPasswordPage extends StatefulWidget {
  const ResetPasswordPage({super.key});

  static const route = '/reset-password';

  @override
  State<ResetPasswordPage> createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final supabase = Supabase.instance.client.auth;

  final passwordController = TextEditingController();

  final Uri? uri = Uri.base;

  bool loading = false;
  bool loadingButton = false;
  bool confirmated = false;

  bool hasToken = false;
  Future<void> getAccessToken() async {
    if (uri == null) return;

    setState(() => loading = true);

    try {
      final token = uri?.queryParameters['token'];
      final email = uri?.queryParameters['email'];

      final response = await supabase.verifyOTP(
        type: OtpType.recovery,
        token: token,
        email: email,
      );

      hasToken = response.session?.accessToken.isNotEmpty ?? false;
    } on AuthException catch (error) {
      if (error.statusCode == '403') {
        AppSnackbar.to.show("Token expirado!");
      }
    } catch (e) {
      debugPrint('ERRO: $e');
      AppSnackbar.to.show('Não foi possível obter o token');
    } finally {
      setState(() => loading = false);
    }

    return;
  }

  Future<void> resetPassword() async {
    setState(() => loadingButton = true);

    if (hasToken == false) {
      await getAccessToken();
    }

    if (hasToken == false) return;

    try {
      final response = await supabase.updateUser(
        UserAttributes(password: passwordController.text.trim()),
      );

      if (response.user != null) {
        setState(() => confirmated = true);

        Get.offAllNamed(SuccessPage.route);

        supabase.signOut();
      } else {
        AppSnackbar.to.show(
          'Não foi possível alterar a senha',
        );
      }
    } on AuthException catch (error) {
      if (error.statusCode == '422') {
        AppSnackbar.to.show(
          "Sua senha precisa ser diferente de uma já usada anteriormente!",
        );
      }
    } catch (error) {
      AppSnackbar.to.show(
        'Não foi possível alterar a senha',
      );
    } finally {
      setState(() => loadingButton = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            const BackgroundAuth(),
            if (loading)
              Center(
                child: SizedBox(
                  width: 60,
                  height: 60,
                  child: CircularProgressIndicator.adaptive(
                    strokeWidth: 3.5,
                    backgroundColor: Colors.white.withOpacity(0.5),
                    valueColor: const AlwaysStoppedAnimation(Colors.white),
                  ),
                ),
              ),
            if (!loading)
              SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      Center(
                        child: AppCardWidget(
                          color: Colors.white70,
                          width: ternaryResponsiveWeb(
                            context: context,
                            caseWeb: MediaQuery.of(context).size.width * 0.6,
                            caseMobile: MediaQuery.of(context).size.width * 0.8,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              children: [
                                CircleAvatar(
                                  radius: 54,
                                  backgroundColor: Colors.black,
                                  child: Image.asset(
                                    "assets/images/logo_without_background.png",
                                    scale: 2.5,
                                  ),
                                ),
                                const SizedBox(height: 40),
                                TextDefault(
                                  text: 'Recuperação de senha',
                                  fontSize: ternaryResponsiveWeb(
                                    context: context,
                                    caseWeb: 26,
                                    caseMobile: 18,
                                  ),
                                  fontWeight: FontWeight.w700,
                                  color: Colors.black,
                                ),
                                const SizedBox(height: 15),
                                AppTextFormField(
                                  fillColor: AppColors.backgroundLight,
                                  textColor: Colors.black,
                                  title: 'Insira sua nova senha',
                                  controller: passwordController,
                                  hintText: 'Nova senha',
                                  isPassword: true,
                                ),
                                const SizedBox(height: 20),
                                AppButtonDefault(
                                  isValid: !confirmated,
                                  onTap: () {
                                    if (passwordController.text.length < 6) {
                                      AppSnackbar.to.show(
                                        'A senha deve ter pelo menos 6 caracteres',
                                      );
                                      return;
                                    }

                                    resetPassword();
                                  },
                                  text: 'Confirmar',
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
