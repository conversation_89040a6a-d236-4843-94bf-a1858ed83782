import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../themes/app_colors.dart';

class AppSnackbar {
  AppSnackbar._();

  static AppSnackbar get to => AppSnackbar._();

  final _defaultDuration = const Duration(seconds: 10);
  final _defaultMargin = const EdgeInsets.fromLTRB(10, 16, 10, 10);
  final _defaultBorderRadius = 8.0;
  final _defaultSnackPosition = SnackPosition.BOTTOM;

  final _defaultBoxShadows = [
    BoxShadow(
      color: const Color.fromARGB(255, 30, 0, 0).withOpacity(0.2),
      offset: const Offset(0, 2),
      blurRadius: 4,
    ),
  ];

  void show(String message, {Widget? mainButton}) {
    Get.rawSnackbar(
      message: message,
      backgroundColor: AppColors.secondary,
      duration: _defaultDuration,
      animationDuration: const Duration(milliseconds: 600),
      margin: _defaultMargin,
      borderRadius: _defaultBorderRadius,
      snackPosition: _defaultSnackPosition,
      boxShadows: _defaultBoxShadows,
      mainButton: mainButton ??
          TextButton(
            onPressed: Get.back,
            child: const Text(
              'Entendi',
              style: TextStyle(
                color: Colors.white,
              ),
            ),
          ),
    );
  }
}
