import 'dart:async';

import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/defaults/user_model.dart';
import '../repositories/auth_repository.dart';

class UserHelper {
  UserHelper._();
  static UserHelper instance = UserHelper._();

  User? get authUser => Supabase.instance.client.auth.currentUser;
  UserModel? _user;
  Future<UserModel?> user([UserModel? initialValue]) async {
    if (authUser == null) return null;

    if (_user == null && initialValue == null) {
      var response = await AuthRepository().getCurrentUser();
      _user = response.data;
    }

    if (initialValue != null) {
      _user = initialValue;
    }

    return _user;
  }

  Future<void> updateCurrentUserData() async {
    var response = await AuthRepository().getCurrentUser();
    _user = response.data;
  }

  Future<void> logout() async {
    await Supabase.instance.client.auth.signOut();
    _user = null;
  }
}
