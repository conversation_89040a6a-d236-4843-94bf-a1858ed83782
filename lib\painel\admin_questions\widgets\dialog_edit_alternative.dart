import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/alternative_model.dart';
import '../../../app/shared/utils/app_snackbar.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/app_button_default.dart';
import '../../../app/widgets/design_system/app_text_form_field.dart';
import '../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../app/widgets/design_system/text_default.dart';
import '../admin_questions_controller.dart';

class DialogEditAlternative extends StatefulWidget {
  const DialogEditAlternative({
    super.key,
    this.alternative,
    required this.questionId,
    required this.prefix,
  });

  final AlternativeModel? alternative;
  final String prefix;
  final int questionId;

  @override
  State<DialogEditAlternative> createState() => _DialogEditAlternativeState();
}

class _DialogEditAlternativeState extends State<DialogEditAlternative> {
  final _formKey = GlobalKey<FormState>();

  final controller = Get.find<AdminQuestionsController>();

  final descriptionAltController = TextEditingController();

  bool correct = false;

  @override
  void initState() {
    super.initState();

    descriptionAltController.addListener(() => setState(() {}));

    Future.microtask(() {
      if (widget.alternative != null) {
        descriptionAltController.text = widget.alternative!.description;
        correct = widget.alternative!.correct;
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    descriptionAltController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: 'Editar alternativa',
      form: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: Column(
          children: [
            AppTextFormField(
              title: 'Descrição',
              controller: descriptionAltController,
              maxLines: 4,
            ),
            const SizedBox(height: 10),
            Row(
              children: [
                const Padding(
                  padding: EdgeInsets.only(left: 3),
                  child: TextDefault(
                    text: 'Correta',
                    fontSize: 14,
                    fontWeight: FontWeight.w800,
                    color: AppColors.backgroundLight,
                  ),
                ),
                Transform.scale(
                  scale: 0.7,
                  child: Switch(
                    activeColor: Colors.blueAccent,
                    value: correct,
                    onChanged: (value) {
                      setState(() => correct = value);
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 30),
            AppButtonDefault(
              isLoading: controller.isLoading,
              isValid: _formKey.currentState?.validate(),
              width: double.maxFinite,
              onTap: () async {
                setState(() {});

                final alternative = AlternativeModel(
                  id: widget.alternative?.id,
                  questionId: widget.questionId,
                  description: descriptionAltController.text.trim(),
                  prefix: widget.prefix,
                  createdAt: widget.alternative?.createdAt ?? DateTime.now(),
                  correct: correct,
                );

                AlternativeModel? result;

                if (widget.alternative != null) {
                  result = await controller.updateAlternative(alternative);
                  Get.back(result: result);

                  if (result == null) {
                    AppSnackbar.to.show(
                      'Não foi possível atualizar a alternativa',
                    );
                  }
                } else {
                  result = await controller.createAlternative(alternative);
                  Get.back(result: result);

                  if (result == null) {
                    AppSnackbar.to.show(
                      'Não foi possível adicionar a alternativa',
                    );
                  }
                }
              },
              text: 'Salvar alterações',
            ),
          ],
        ),
      ),
    );
  }
}
