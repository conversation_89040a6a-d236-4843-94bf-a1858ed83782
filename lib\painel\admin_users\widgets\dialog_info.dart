import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:painel_dos_aprovados/app/widgets/design_system/app_button_default.dart';

import '../../../app/infra/models/contest_model.dart';
import '../../../app/infra/models/defaults/user_model.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../app/widgets/design_system/text_default.dart';
import '../../widgets/info_item.dart';
import '../admin_users_controller.dart';

class DialogInfo extends StatelessWidget {
  const DialogInfo({super.key, required this.user});

  final UserModel user;

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: 'Informações do usuário',
      height: MediaQuery.of(context).size.height * 0.75,
      form: Expanded(
        child: SingleChildScrollView(
          physics: const BouncingScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InfoItem(data: 'Nome', value: user.name),
              const SizedBox(height: 10),
              InfoItem(data: 'Whatsapp', value: user.phone),
              const SizedBox(height: 10),
              InfoItem(
                data: 'Registrado desde',
                value: DateFormat('dd/MM/yyyy').format(user.createdAt),
              ),
              const SizedBox(height: 10),
              const TextDefault(
                text: 'Situação atual',
                color: AppColors.cardPrimaryLight,
                fontSize: 10,
              ),
              Column(
                children: Get.find<AdminUsersController>()
                    .contests
                    .map((contest) => ContestWidget(u: user, c: contest))
                    .toList(),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class ContestWidget extends StatefulWidget {
  const ContestWidget({super.key, required this.u, required this.c});

  final UserModel u;
  final ContestModel c;

  @override
  State<ContestWidget> createState() => _ContestWidgetState();
}

class _ContestWidgetState extends State<ContestWidget> {
  final control = Get.find<AdminUsersController>();
  bool loading = true;
  bool isReleased = false;

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      isReleased = await control.userHasThisContest(
        productId: widget.c.kiwifyProductId,
        contestId: widget.c.id!,
        userId: widget.u.id!,
        email: widget.u.email,
      );
      loading = false;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      color: AppColors.secondary,
      elevation: 20,
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                TextDefault(
                  text: widget.c.name,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 16,
                ),
                TextDefault(
                  text: widget.c.description,
                  overflow: TextOverflow.ellipsis,
                  fontSize: 12,
                ),
              ],
            ),
            Visibility(
              visible: !loading,
              replacement: const CircularProgressIndicator(),
              child: AppButtonDefault(
                onTap: () async {
                  loading = true;
                  setState(() {});
                  isReleased = await control.releaseOrBlockContestForTheUser(
                    user: widget.u,
                    contest: widget.c,
                    userHasThisContest: isReleased,
                  );
                  loading = false;
                  setState(() {});
                },
                text: isReleased ? "Bloquear" : "Liberar",
                buttonColor:
                    isReleased ? AppColors.primaryRed : AppColors.primaryGreen,
                isValid: true,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
