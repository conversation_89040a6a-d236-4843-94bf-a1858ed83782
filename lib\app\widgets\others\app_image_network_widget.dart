import 'package:flutter/material.dart';

import '../../shared/utils/ternary_clean.dart';
import '../../themes/app_colors.dart';

class AppImageNetworkWidget extends StatelessWidget {
  final String urlImage;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double? scale;
  final Animation<double>? opacity;
  final FilterQuality filterQuality;

  const AppImageNetworkWidget(
    this.urlImage, {
    super.key,
    this.height,
    this.width,
    this.fit,
    this.opacity,
    this.scale,
    this.filterQuality = FilterQuality.low,
  });
  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(16),
      child: ternaryClean(
        condition: urlImage.isNotEmpty,
        caseTrue: Image.network(
          urlImage,
          height: height,
          width: width,
          fit: fit,
          scale: scale ?? 1,
          opacity: opacity,
          filterQuality: filterQuality,
          loadingBuilder: (context, child, loadingProgress) {
            if (loadingProgress == null) {
              return child;
            }
            return SizedBox(
              width: 100,
              height: 100,
              child: Center(
                child: CircularProgressIndicator(
                  color: AppColors.primary,
                  value: loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
                ),
              ),
            );
          },
          errorBuilder: (context, error, stackTrace) {
            return Image.asset(
              'assets/images/logo_without_background.png',
              height: height,
              width: width,
            );
          },
        ),
        caseFalse: Image.asset(
          'assets/images/logo_without_background.png',
          height: height,
          width: width,
        ),
      ),
    );
  }
}
