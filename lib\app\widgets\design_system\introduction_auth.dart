import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'text_default.dart';

class IntroductionAuth extends StatelessWidget {
  final String text;

  const IntroductionAuth({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Image.asset(
          "assets/images/logo_login.png",
          width: Get.width * 0.85,
        ),
        const SizedBox(height: 20),
        CircleAvatar(
          radius: 54,
          backgroundColor: Colors.black,
          child: Image.asset(
            "assets/images/logo_without_background.png",
            scale: 2.5,
          ),
        ),
        const SizedBox(height: 40),
        TextDefault(
          text: text,
          fontSize: 26,
          fontWeight: FontWeight.w700,
          color: Colors.white,
        ),
        const SizedBox(height: 5),
      ],
    );
  }
}
