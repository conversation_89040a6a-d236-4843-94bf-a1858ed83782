import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/widgets/design_system/confirmation_dialog.dart';
import '../../app/widgets/states/app_widget_empty.dart';
import '../../app/widgets/states/app_widget_loading.dart';
import '../widgets/admin_responsive_body.dart';
import 'admin_questions_controller.dart';
import 'widgets/add_question_button.dart';
import 'widgets/dialog_add_edit_question.dart';
import 'widgets/info_and_button_back.dart';
import 'widgets/question_widget.dart';

class AdminQuestionsPage extends StatelessWidget {
  const AdminQuestionsPage({super.key});

  static const route = '/questoes';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AdminResponsiveBody(
        title: 'Questões',
        children: [
          SizedBox(
            height: MediaQuery.of(context).size.height * 0.8,
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const InfoAndButtonBack(),
                  const <PERSON><PERSON><PERSON><PERSON>(height: 20),
                  GetBuilder<AdminQuestionsController>(
                    builder: (control) {
                      if (control.isLoading) return const AppWidgetLoading();

                      if (control.questions.isEmpty) {
                        return const AppWidgetEmpty(
                          message: 'Não há questões cadastradas nessa matéria',
                          showImage: true,
                        );
                      }

                      control.questions
                          .sort((a, b) => b.number.compareTo(a.number));

                      return Column(
                        children: control.questions.map((q) {
                          return QuestionWidget(
                            number: q.number,
                            question: q,
                            pressDelete: () {
                              Get.dialog(
                                ConfirmationDialog(
                                  message: 'Tem certeza que deseja '
                                      'excluir essa questão?',
                                  subtitle: 'Essa ação não poderá '
                                      'ser desfeita.',
                                  onConfirm: () async {
                                    control.deleteQuestion(questionId: q.id!);
                                  },
                                ),
                              );
                            },
                            pressEdit: () {
                              Get.dialog(DialogAddEditQuestion(question: q));
                            },
                          );
                        }).toList(),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: const AddQuestionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
    );
  }
}
