import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';
import '../design_system/app_button_default.dart';
import '../design_system/text_default.dart';

class AppWidgetEmpty extends StatelessWidget {
  const AppWidgetEmpty({
    super.key,
    required this.message,
    this.showButtonBack = false,
    this.showImage = false,
    this.description = '',
    this.heightImage,
  });

  final bool showButtonBack;
  final bool showImage;
  final String message;
  final String description;
  final double? heightImage;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Visibility(
          visible: showImage,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(25),
            child: Image.asset(
              'assets/images/empty.png',
              height: heightImage ?? 250,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: TextDefault(
            text: message,
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: AppColors.backgroundLight,
            textAlign: TextAlign.center,
          ),
        ),
        Visibility(
          visible: description.isNotEmpty,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            child: Text(
              description,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ),
        Visibility(
          visible: showButtonBack,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 50),
            child: AppButtonDefault(
              onTap: () {
                Navigator.pop(context);
              },
              text: 'Voltar',
              isValid: true,
            ),
          ),
        ),
      ],
    );
  }
}
