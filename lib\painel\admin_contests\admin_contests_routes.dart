import 'package:get/get.dart';

import '../../app/infra/repositories/contests_repository.dart';
import '../../app/infra/repositories/matters_repository.dart';
import '../../app/shared/middlewares/auth_middleware.dart';
import 'admin_contests_controller.dart';
import 'admin_contests_page.dart';

List<GetPage> adminContestsRoutes = [
  GetPage(
    name: AdminContestsPage.route,
    page: () => const AdminContestsPage(),
    binding: BindingsBuilder(() {
      Get.lazyPut(() => ContestsRepository());
      Get.lazyPut(() => MattersRepository());
      Get.put(AdminContestsController(Get.find(), Get.find()));
    }),
    middlewares: [AuthMiddleware()],
  ),
];
