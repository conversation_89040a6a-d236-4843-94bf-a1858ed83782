import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/infra/models/contest_model.dart';
import '../../app/infra/models/matter_model.dart';
import '../../app/infra/repositories/contests_repository.dart';
import '../../app/infra/repositories/matters_repository.dart';
import '../../app/shared/mixins/loader_manager.dart';
import '../../app/shared/utils/app_snackbar.dart';

class AdminContestsController extends GetxController with LoaderManagerMixin {
  final ContestsRepository _contestsRepository;
  final MattersRepository _mattersRepository;
  AdminContestsController(this._contestsRepository, this._mattersRepository);

  static AdminContestsController get to => Get.find();

  final contestNameController = TextEditingController();
  final groupLinkController = TextEditingController();

  List<ContestModel> contests = [];

  @override
  void onInit() {
    super.onInit();
    getContests();
  }

  //CRUD MATTERS
  Future<MatterModel?> createMatter(MatterModel matter) async {
    changeLoading(true);

    final response = await _mattersRepository.createMatter(matter: matter);

    changeLoading(false);

    if (response.success) {
      return response.data;
    } else {
      return null;
    }
  }

  Future<MatterModel?> updateMatter(MatterModel matter) async {
    changeLoading(true);

    final response = await _mattersRepository.updateMatter(matter: matter);

    changeLoading(false);

    if (response.success) {
      return matter;
    } else {
      return null;
    }
  }

  Future<bool> deleteMatter({required int matterId}) async {
    changeLoading(true);

    final response = await _mattersRepository.deleteMatter(matterId: matterId);

    changeLoading(false);

    return response.success;
  }

  //CRUD CONTESTS
  Future<void> createContest(
    ContestModel contest, {
    required PlatformFile? imageFile,
  }) async {
    final response = await _contestsRepository.createContest(
        contest: contest, imageFile: imageFile);

    if (response.success) {
      contests.add(response.data!);
      Get.back(result: imageFile != null);
    } else {
      AppSnackbar.to.show(response.message);
    }

    update();
  }

  Future<void> updateContest(
    ContestModel contest, {
    required PlatformFile? imageFile,
  }) async {
    changeLoading(true);
    final response = await _contestsRepository.updateContest(
      contest: contest,
      imageFile: imageFile,
    );

    if (response.success) {
      contests.removeWhere((c) => c.id == contest.id);
      contests.add(response.data!);
      Get.back(result: imageFile != null);
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  void deleteContest({required int contestId}) async {
    changeLoading(true);

    final response = await _contestsRepository.deleteContest(
      contestId: contestId,
    );

    if (response.success) {
      contests.removeWhere((c) => c.id == contestId);
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  //GET DATA
  void getContests() async {
    changeLoading(true);

    final response = await _contestsRepository.getContests();

    if (response.success) {
      contests = response.data ?? [];
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  Future<List<MatterModel>> getMatters({required String contestId}) async {
    final response = await _mattersRepository.getMatters(contestId: contestId);

    if (!response.success) AppSnackbar.to.show(response.message);

    return response.data ?? [];
  }

  //PHOTOS
  Future<PlatformFile?> getFileImageGallery() async {
    FilePickerResult? result = await FilePicker.platform.pickFiles();

    if (result == null) {
      AppSnackbar.to.show('Nenhum arquivo selecionado');
      return null;
    }

    return result.files.first;
  }
}
