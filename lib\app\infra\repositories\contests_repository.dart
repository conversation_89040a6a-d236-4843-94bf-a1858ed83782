import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/contest_model.dart';
import '../models/defaults/response_model.dart';
import '../models/defaults/user_model.dart';

class ContestsRepository {
  final _supabase = Supabase.instance.client.from('tb_contests');
  final _supabasePayments = Supabase.instance.client.from('tb_kiwify_payments');
  final _userContests = Supabase.instance.client.from('tb_user_contests');

  Future<ResponseModel<List<ContestModel>>> getContests() async {
    try {
      List<ContestModel> list = [];

      final data = await _supabase.select().order('order', ascending: true);

      for (var doc in data) {
        list.add(ContestModel.fromMap(doc));
      }

      return ResponseModel.success(list);
    } catch (e) {
      debugPrint('Erro $e');
      return ResponseModel.error('Não foi possível buscar os concursos');
    }
  }

  Future<ResponseModel<ContestModel>> createContest({
    required ContestModel contest,
    required PlatformFile? imageFile,
  }) async {
    try {
      final response =
          await _supabase.insert(contest.toMap()).select().single();

      final data = ContestModel.fromMap(response);

      if (imageFile != null) {
        final storage = Supabase.instance.client.storage;

        final bytes = imageFile.bytes;
        final path = 'contests/${data.id}';

        await storage.from('images').uploadBinary(
              path,
              bytes!,
              fileOptions: FileOptions(
                contentType: 'image/${imageFile.extension}',
              ),
            );

        final url = storage.from('images').getPublicUrl(path);

        await _supabase.update({'image': url}).eq('id', data.id!);

        data.image = url;
      }

      return ResponseModel.success(data);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao registrar concurso');
    }
  }

  Future<ResponseModel<ContestModel>> updateContest({
    required ContestModel contest,
    required PlatformFile? imageFile,
  }) async {
    try {
      await _supabase.update(contest.toMap()).eq('id', contest.id!);

      if (imageFile != null) {
        final storage = Supabase.instance.client.storage;

        final bytes = imageFile.bytes;
        final path = 'contests/${contest.id}';

        if (contest.image.isNotEmpty) {
          await storage.from('images').updateBinary(
                path,
                bytes!,
                fileOptions: FileOptions(
                  contentType: 'image/${imageFile.extension}',
                ),
              );
        } else {
          await storage.from('images').uploadBinary(
                path,
                bytes!,
                fileOptions: FileOptions(
                  contentType: 'image/${imageFile.extension}',
                ),
              );
        }

        final url = storage.from('images').getPublicUrl(path);

        await _supabase.update({'image': url}).eq('id', contest.id!);

        contest.image = url;
      }

      return ResponseModel.success(contest);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao atualizar concurso');
    }
  }

  Future<ResponseModel<bool>> deleteContest({required int contestId}) async {
    try {
      await _supabase.delete().eq('id', contestId);

      final storage = Supabase.instance.client.storage;
      await storage.from('images').remove(['contests/$contestId']);

      return ResponseModel.success(true);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao excluir concurso');
    }
  }

  Future<ResponseModel<ContestModel>> getContest({
    required String contestId,
  }) async {
    try {
      final response = await _supabase.select().eq('id', contestId).single();

      return ResponseModel.success(ContestModel.fromMap(response));
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao obter concurso');
    }
  }

  Future<ResponseModel<bool>> userHasThisContest({
    required String productId,
    required int contestId,
    required int userId,
    required String email,
  }) async {
    try {
      final response = await _supabasePayments
          .select('subscription_status')
          .match({
            'product_id': productId,
            'email': email,
          })
          .eq('subscription_status', 'active')
          .limit(1);

      var value = response.isNotEmpty;

      final item = await _userContests.select('id').match({
        'contest_id': contestId,
        'user_id': userId,
      }).limit(1);

      if (item.isNotEmpty) value = true;

      return ResponseModel.success(value);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao obter dados dos concursos');
    }
  }

  Future<ResponseModel> releaseOrBlockContestForTheUser({
    required UserModel user,
    required ContestModel contest,
    required bool userHasThisContest,
  }) async {
    try {
      var data = {
        'contest_id': contest.id!,
        'user_id': user.id!,
      };

      final dataKiwify = await _supabasePayments.select('id').match({
        'email': user.email,
        'product_id': contest.kiwifyProductId,
      });

      if (dataKiwify.isNotEmpty) {
        final id = dataKiwify.first['id'];
        var newStatus = userHasThisContest ? 'active' : 'deactive';

        await _supabasePayments
            .update({'subscription_status': newStatus})
            .eq('id', id)
            .select();
      } else {
        final item = await _userContests.select().match(data);

        if (item.isEmpty) {
          await _userContests.insert(data);
        } else {
          await _userContests.delete().eq('id', item.first['id']);
        }
      }

      return ResponseModel.success(null);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Ocorreu um erro');
    }
  }
}
