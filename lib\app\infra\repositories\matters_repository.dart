import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/defaults/response_model.dart';
import '../models/matter_model.dart';

class MattersRepository {
  final _supabaseQuestions = Supabase.instance.client.from('tb_questions');
  final _supabase = Supabase.instance.client.from('tb_matters');

  Future<ResponseModel<List<MatterModel>>> getMatters({
    required String contestId,
  }) async {
    try {
      List<MatterModel> list = [];

      final data = await _supabase
          .select()
          .eq('contest_id', contestId)
          .order('order', ascending: true);

      for (var doc in data) {
        list.add(MatterModel.fromMap(doc));
      }

      return ResponseModel.success(list);
    } catch (e) {
      debugPrint('Erro $e');
      return ResponseModel.error('Não foi possível buscar as matérias');
    }
  }

  Future<ResponseModel<int>> getCountQuestions(int matterId) async {
    try {
      final res = await _supabaseQuestions
          .select()
          .eq('matter_id', matterId)
          .count(CountOption.exact);

      int count = res.count;
      return ResponseModel.success(count);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Não foi possível buscar as questões');
    }
  }

  Future<ResponseModel<MatterModel>> createMatter({
    required MatterModel matter,
  }) async {
    try {
      final response = await _supabase.insert(matter.toMap()).select().single();

      return ResponseModel.success(MatterModel.fromMap(response));
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao registrar matéria');
    }
  }

  Future<ResponseModel<bool>> updateMatter({
    required MatterModel matter,
  }) async {
    try {
      await _supabase.update(matter.toMap()).eq('id', matter.id!);

      return ResponseModel.success(true);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao atualizar matéria');
    }
  }

  Future<ResponseModel<bool>> deleteMatter({required int matterId}) async {
    try {
      await _supabase.delete().eq('id', matterId);

      return ResponseModel.success(true);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao excluir matéria');
    }
  }

  Future<ResponseModel<MatterModel>> getMatter({
    required String matterId,
  }) async {
    try {
      final response = await _supabase.select().eq('id', matterId).single();

      return ResponseModel.success(MatterModel.fromMap(response));
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error('Erro ao registrar matéria');
    }
  }
}
