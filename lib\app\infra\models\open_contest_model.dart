class OpenContestModel {
  final int? id;
  final DateTime createdAt;
  final String name;
  final String state;
  final String salary;
  final String vacancies;
  final String examBoard;
  final String cnh;
  final String age;
  final String subscriptionDate;
  final String examDate;
  final Map<String, dynamic> action;
  final int? contestId;

  OpenContestModel({
    required this.id,
    required this.createdAt,
    required this.name,
    required this.state,
    required this.salary,
    required this.vacancies,
    required this.examBoard,
    required this.cnh,
    required this.age,
    required this.subscriptionDate,
    required this.examDate,
    required this.action,
    this.contestId,
  });
  factory OpenContestModel.fromMap(Map<String, dynamic> map) {
    return OpenContestModel(
      id: map['id'],
      createdAt: DateTime.parse(map['createdAt']),
      name: map['name'] ?? '',
      state: map['state'] ?? '',
      salary: map['salary'] ?? '',
      vacancies: map['vacancies'] ?? '',
      examBoard: map['examBoard'] ?? '',
      cnh: map['cnh'] ?? '',
      age: map['age'] ?? '',
      subscriptionDate: map['subscriptionDate'] ?? '',
      examDate: map['examDate'] ?? '',
      action: Map<String, dynamic>.from(map['action'] ?? {}),
      contestId: map['contestId'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'createdAt': createdAt.toIso8601String(),
      'name': name,
      'state': state,
      'salary': salary,
      'vacancies': vacancies,
      'examBoard': examBoard,
      'cnh': cnh,
      'age': age,
      'subscriptionDate': subscriptionDate,
      'examDate': examDate,
      'action': action,
      'contestId': contestId,
    };
  }
}
