import 'package:flutter/material.dart';

class AppImageBuilder extends StatelessWidget {
  final String urlImage;
  final String assetImageCaseEmpty;
  final Widget? imageWidget;

  const AppImageBuilder({
    Key? key,
    required this.urlImage,
    required this.assetImageCaseEmpty,
    this.imageWidget,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (urlImage.isEmpty) {
      return Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          image: DecorationImage(
            image: AssetImage(assetImageCaseEmpty),
            fit: BoxFit.cover,
          ),
        ),
      );
    }
    return FutureBuilder(
      future: precacheImage(
        NetworkImage(urlImage),
        context,
      ),
      builder: (
        BuildContext context,
        AsyncSnapshot<dynamic> snapshot,
      ) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const CircularProgressIndicator();
        } else if (snapshot.hasError) {
          return const SizedBox.shrink();
        } else {
          if (imageWidget != null) return imageWidget!;
          return Container(
            decoration: BoxDecoration(
              color: Colors.red,
              borderRadius: BorderRadius.circular(8),
              image: DecorationImage(
                image: NetworkImage(urlImage),
                fit: BoxFit.cover,
              ),
            ),
          );
        }
      },
    );
  }
}
