import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';

class AppCardWidget extends StatelessWidget {
  final Widget child;
  final Color? color;
  final Color? borderColor;
  final double? width;
  final double? height;
  final double? elevation;

  const AppCardWidget({
    required this.child,
    super.key,
    this.color,
    this.borderColor,
    this.width,
    this.height,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? double.maxFinite,
      height: height,
      child: Card(
        elevation: elevation,
        margin: EdgeInsets.zero,
        shape: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: BorderSide(
            color: borderColor ?? AppColors.secondary,
          ),
        ),
        color: color ?? AppColors.secondary,
        child: child,
      ),
    );
  }
}
