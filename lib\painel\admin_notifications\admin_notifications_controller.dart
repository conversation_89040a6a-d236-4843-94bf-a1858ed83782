import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:painel_dos_aprovados/app/shared/mixins/loader_manager.dart';
import 'package:painel_dos_aprovados/app/shared/utils/app_snackbar.dart';

import '../../app/infra/models/notification_model.dart';
import '../../app/infra/repositories/notifications_repository.dart';
import '../../app/widgets/design_system/confirmation_dialog.dart';

class AdminNotificationsController extends GetxController with LoaderManagerMixin {
  final NotificationsRepository _notificationsRepository;
  final titleController = TextEditingController();
  final messageController = TextEditingController();

  AdminNotificationsController(this._notificationsRepository);

  List<NotificationModel> notifications = [];

  @override
  void onInit() {
    super.onInit();
    loadNotifications();
    titleController.addListener(() => update());
    messageController.addListener(() => update());
  }

  @override
  void onClose() {
    titleController.dispose();
    messageController.dispose();
    super.onClose();
  }

  Future<void> loadNotifications() async {
    changeLoading(true);

    final response = await _notificationsRepository.getNotifications();
    notifications = response.data ?? [];

    if (!response.success) AppSnackbar.to.show(response.message);

    changeLoading(false);
  }

  Future<void> createNotification({
    required String title,
    required String body,
  }) async {
    final confirm = await Get.dialog<bool>(
      ConfirmationDialog(
        message: 'Tem certeza que deseja enviar esta notificação?\n\n'
            'ATENÇÃO: Após confirmado, todos os usuários receberão '
            'a notificação e não será possível cancelar.',
        onConfirm: () => Get.back(result: true),
        autoClose: false,
      ),
    );

    if (confirm != true) return;

    changeLoading(true);
    final response = await _notificationsRepository.sendNotification(
      title: title,
      body: body,
      topic: 'geral',
    );
    changeLoading(false);

    if (response.success) {
      _createNotification(title, body);
    } else {
      AppSnackbar.to.show(response.message);
    }
  }

  Future<void> _createNotification(String title, String body) async {
    if (title.isEmpty || body.isEmpty) {
      AppSnackbar.to.show('Título e mensagem são obrigatórios');
      return;
    }

    changeLoading(true);
    final response = await _notificationsRepository.createNotification(
      title: title,
      body: body,
    );
    changeLoading(false);

    if (response.success) {
      notifications.insert(0, response.data!);
      update();
    } else {
      AppSnackbar.to.show(response.message);
    }
  }

  Future<void> deleteNotification(int id) async {
    try {
      final confirm = await Get.dialog<bool>(
        ConfirmationDialog(
          message: 'Tem certeza que deseja excluir esta notificação?\n\n'
              'ATENÇÃO: Apenas o registro da notificação será removido do banco de dados. '
              'A notificação permanecerá na central de notificações dos usuários até que '
              'eles mesmos a removam.',
          autoClose: false,
          onConfirm: () => Get.back(result: true),
        ),
      );

      if (confirm != true) return;

      changeLoading(true);
      final response = await _notificationsRepository.deleteNotification(id);
      changeLoading(false);

      if (response.success) {
        notifications.removeWhere((element) => element.id == id);
        update();
      } else {
        AppSnackbar.to.show(response.message);
      }
    } catch (e) {
      AppSnackbar.to.show(e.toString());
    }
  }
}
