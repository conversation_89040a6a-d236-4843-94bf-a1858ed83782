import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/open_contest_model.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/confirmation_dialog.dart';
import '../admin_open_contests_controller.dart';
import 'add_edit_open_contest/dialog_add_edit_open_contest.dart';

class OpenContestWidget extends StatefulWidget {
  const OpenContestWidget({super.key, required this.contest});

  final OpenContestModel contest;

  @override
  State<OpenContestWidget> createState() => _OpenContestWidgetState();
}

class _OpenContestWidgetState extends State<OpenContestWidget> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {},
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: AppColors.secondary,
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.contest.name,
                      style: const TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 19,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    const SizedBox(height: 10),
                    Text(
                      'Estado: ${widget.contest.state}\nBanca: ${widget.contest.examBoard}\nVagas: ${widget.contest.vacancies}\nSalário: ${widget.contest.salary}\nCNH: ${widget.contest.cnh}\nIdade: ${widget.contest.age}\nData de inscrição: ${widget.contest.subscriptionDate}\nData do exame: ${widget.contest.examDate}',
                      style: const TextStyle(
                        fontFamily: 'Roboto',
                        fontSize: 14,
                        color: Colors.white,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 10),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton.outlined(
                    onPressed: () => Get.dialog(
                      DialogAddEditOpenContest(
                        contest: widget.contest,
                      ),
                    ),
                    icon: const Icon(
                      Icons.edit,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 10),
                  GetBuilder<AdminOpenContestsController>(
                    builder: (control) {
                      return IconButton.outlined(
                        onPressed: () {
                          Get.dialog(
                            ConfirmationDialog(
                              message: 'Tem certeza que deseja '
                                  'excluir esse edital e todos '
                                  'os itens cadastrados nele?',
                              subtitle: 'Essa ação não poderá '
                                  'ser desfeita.',
                              autoClose: false,
                              onConfirm: () async {
                                control.deleteContest(
                                  widget.contest.id!,
                                );
                              },
                            ),
                          );
                        },
                        icon: const Icon(
                          Icons.delete,
                          color: Colors.white,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
