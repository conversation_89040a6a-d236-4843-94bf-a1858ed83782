import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/question_model.dart';
import '../../../app/shared/utils/app_snackbar.dart';
import '../../../app/widgets/design_system/app_button_default.dart';
import '../../../app/widgets/design_system/app_text_form_field.dart';
import '../../../app/widgets/design_system/dialog_default_widget.dart';
import '../admin_questions_controller.dart';

class DialogAddEditQuestion extends StatefulWidget {
  const DialogAddEditQuestion({super.key, this.question});

  final QuestionModel? question;

  @override
  State<DialogAddEditQuestion> createState() => _DialogAddEditQuestionState();
}

class _DialogAddEditQuestionState extends State<DialogAddEditQuestion> {
  final _formKey = GlobalKey<FormState>();

  final controller = Get.find<AdminQuestionsController>();

  final descriptionController = TextEditingController();
  final commentController = TextEditingController();
  final yearController = TextEditingController();
  final boardController = TextEditingController();
  final numberController = TextEditingController();
  final cityController = TextEditingController();

  @override
  void initState() {
    super.initState();

    descriptionController.addListener(() => setState(() {}));

    Future.microtask(() {
      if (widget.question != null) {
        descriptionController.text = widget.question!.name;
        commentController.text = widget.question!.comment ?? '';
        cityController.text = widget.question!.cityOrState ?? '';
        yearController.text = widget.question?.year != null
            ? widget.question!.year.toString()
            : '';
        boardController.text = widget.question!.board;
        numberController.text = widget.question?.number != null
            ? widget.question!.number.toString()
            : '';

        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    descriptionController.dispose();
    commentController.dispose();
    yearController.dispose();
    boardController.dispose();
    numberController.dispose();
    cityController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: widget.question != null ? 'Editar questão' : 'Adicionar questão',
      height: MediaQuery.of(context).size.height * 0.75,
      form: Expanded(
        child: Form(
          key: _formKey,
          autovalidateMode: AutovalidateMode.onUserInteraction,
          child: SingleChildScrollView(
            child: Column(
              children: [
                AppTextFormField(
                  title: 'Número da questão',
                  controller: numberController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  optional: true,
                  title: 'Banca',
                  controller: boardController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  optional: true,
                  title: 'Ano',
                  controller: yearController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(4),
                  ],
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  optional: true,
                  title: 'Cidade e/ou estado',
                  controller: cityController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Enunciado',
                  controller: descriptionController,
                  maxLines: 5,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  optional: true,
                  title: 'Comentário',
                  controller: commentController,
                  maxLines: 5,
                ),
                const SizedBox(height: 30),
                AppButtonDefault(
                  isLoading: controller.isLoading,
                  isValid: _formKey.currentState?.validate(),
                  width: double.maxFinite,
                  onTap: () async {
                    setState(() {});

                    final year = yearController.text;

                    if (year.isNotEmpty && year.length < 4) {
                      AppSnackbar.to.show('Insira o ano no formato correto');
                      return;
                    }

                    final question = QuestionModel(
                      id: widget.question?.id,
                      name: descriptionController.text.trim(),
                      matterId: controller.matter!.id!,
                      comment: commentController.text.trim(),
                      createdAt: widget.question?.createdAt ?? DateTime.now(),
                      year: year.isNotEmpty ? int.parse(year) : null,
                      number: int.parse(numberController.text),
                      board: boardController.text.trim(),
                      cityOrState: cityController.text.isNotEmpty
                          ? cityController.text
                          : null,
                    );

                    if (widget.question != null) {
                      controller.updateQuestion(question);
                    } else {
                      controller.createQuestion(question);
                    }

                    setState(() {});
                  },
                  text: widget.question != null
                      ? 'Salvar alterações'
                      : 'Adicionar',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
