import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/contest_model.dart';
import '../models/dashboard_users_model.dart';
import '../models/defaults/response_model.dart';
import '../models/ranking/ranking_item_model.dart';

class DashboardRepository {
  final _supabaseUsers = Supabase.instance.client.from('tb_users');
  final _supabaseContests = Supabase.instance.client.from('tb_contests');
  final _supabaseMatters = Supabase.instance.client.from('tb_matters');
  final _supabaseQuestions = Supabase.instance.client.from('tb_questions');
  final _supabasePayments = Supabase.instance.client.from('tb_kiwify_payments');
  final _supabaseUsersContests =
      Supabase.instance.client.from('tb_user_contests');

  Future<ResponseModel<int>> getCountContests() async {
    try {
      final res = await _supabaseContests.select().count(CountOption.exact);

      int count = res.count;
      return ResponseModel.success(count);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error(
        'Não foi possível buscar o número de concursos',
      );
    }
  }

  Future<ResponseModel<List<ContestModel>>> getContests() async {
    try {
      List<ContestModel> list = [];

      final dataContests = await _supabaseContests.select();

      for (var doc in dataContests) {
        final item = ContestModel.fromMap(doc);

        // GET MATTERS COUNT
        final responseMatters = await _supabaseMatters
            .select()
            .eq('contest_id', doc['id'])
            .count(CountOption.exact);
        item.qtMatters = responseMatters.count;

        if (doc['kiwify_product_id'] != null) {
          // GET USERS COUNT
          final responsePayments = await _supabasePayments.select().match({
            'product_id': doc['kiwify_product_id'],
            'subscription_status': 'active',
          }).count(CountOption.exact);

          final responseContests = await _supabaseUsersContests
              .select()
              .eq('contest_id', doc['id'])
              .count(CountOption.exact);

          item.qtUsers = responsePayments.count + responseContests.count;
        }

        list.add(item);
      }

      return ResponseModel.success(list);
    } catch (e) {
      debugPrint('Erro $e');
      return ResponseModel.error('Não foi possível buscar os concursos');
    }
  }

  Future<ResponseModel<int>> getCountMatters() async {
    try {
      final res = await _supabaseMatters.select().count(CountOption.exact);

      int count = res.count;
      return ResponseModel.success(count);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error(
        'Não foi possível buscar o número de matérias',
      );
    }
  }

  Future<ResponseModel<int>> getCountQuestions() async {
    try {
      final res = await _supabaseQuestions.select().count(CountOption.exact);

      int count = res.count;
      return ResponseModel.success(count);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error(
        'Não foi possível buscar o número de matérias',
      );
    }
  }

  Future<ResponseModel<DashboardUsersModel>> getCountUsers() async {
    try {
      final responsePayments =
          await _supabasePayments.select().eq('subscription_status', 'active');

      final responseContests = await _supabaseUsersContests.select();

      final responseUsers =
          await _supabaseUsers.select().count(CountOption.exact);

      int usersWithPlan = responsePayments.length + responseContests.length;

      var result = DashboardUsersModel(
        usersWithPlan: usersWithPlan,
        usersWithoutPlan: responseUsers.count - usersWithPlan,
      );

      return ResponseModel.success(result);
    } catch (e) {
      debugPrint('Erro: $e');
      return ResponseModel.error(
        'Não foi possível buscar o número de usuários registrados',
      );
    }
  }

  Future<List<RankingItemModel>> getLastMonthRanking() async {
    try {
      List<RankingItemModel> list = [];

      var data = await Supabase.instance.client.rpc(
        'get_last_month_ranking',
      );

      for (var item in data) {
        list.add(RankingItemModel.fromMap(item));
      }

      return list;
    } catch (e) {
      return [];
    }
  }
}
