import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../themes/app_colors.dart';
import '../../../../../widgets/design_system/text_default.dart';
import '../send_email_recover_password_controller.dart';

class ResendButton extends StatelessWidget {
  const ResendButton({super.key, required this.valid});

  final bool valid;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SendEmailRecoverPasswordController>(
      builder: (control) {
        return Column(
          children: [
            TextDefault(
              text: control.formatTime(),
              fontSize: 20,
            ),
            const SizedBox(height: 8),
            Container(
              decoration: BoxDecoration(
                color: valid ? Colors.black : Colors.grey,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(0),
                child: TextButton.icon(
                  onPressed: control.isLoading ? () {} : () => control.resend(),
                  icon: Icon(
                    Icons.replay,
                    size: 26,
                    color: valid
                        ? AppColors.primary
                        : AppColors.primary.withOpacity(0.5),
                  ),
                  label: TextDefault(
                    text: 'Reenviar',
                    fontSize: 16,
                    fontWeight: FontWeight.w800,
                    color: valid
                        ? AppColors.primary
                        : AppColors.primary.withOpacity(0.5),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
