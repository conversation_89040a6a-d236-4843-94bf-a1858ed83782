import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/question_model.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/text_default.dart';
import 'dialog_question_info.dart';

class QuestionWidget extends StatelessWidget {
  const QuestionWidget({
    super.key,
    required this.question,
    required this.pressDelete,
    required this.pressEdit,
    required this.number,
  });

  final QuestionModel question;
  final int number;
  final VoidCallback pressDelete;
  final VoidCallback pressEdit;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Get.dialog(DialogQuestionInfo(question: question)),
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 3),
        child: Card(
          color: AppColors.secondary,
          child: SizedBox(
            width: double.maxFinite,
            child: Padding(
              padding: const EdgeInsets.all(10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Wrap(
                    spacing: 20,
                    runSpacing: 5,
                    children: [
                      TextCustom(title: 'Banca', value: question.board),
                      TextCustom(
                        title: 'Ano',
                        value: question.year.toString(),
                      ),
                    ],
                  ),
                  const SizedBox(height: 15),
                  TextDefault(
                    text: '$number. ${question.name}',
                  ),
                  const SizedBox(height: 15),
                  Center(
                    child: Wrap(
                      alignment: WrapAlignment.center,
                      runAlignment: WrapAlignment.center,
                      spacing: 15,
                      runSpacing: 15,
                      children: [
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: AppColors.primary,
                          ),
                          child: IconButton(
                            onPressed: pressEdit,
                            icon: const Icon(
                              Icons.edit,
                              color: AppColors.backgroundLight,
                            ),
                          ),
                        ),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            color: AppColors.primary,
                          ),
                          child: IconButton(
                            onPressed: pressDelete,
                            icon: const Icon(
                              Icons.delete,
                              color: AppColors.backgroundLight,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class TextCustom extends StatelessWidget {
  const TextCustom({
    super.key,
    required this.title,
    required this.value,
  });

  final String title;
  final String? value;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        TextDefault(
          text: '$title: ',
          fontWeight: FontWeight.w800,
        ),
        TextDefault(
          text: value != null && value!.isNotEmpty && value != 'null'
              ? value!
              : ' -',
        ),
      ],
    );
  }
}
