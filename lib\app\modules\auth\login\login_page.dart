import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../widgets/design_system/app_button_default.dart';
import '../../../widgets/design_system/app_text_form_field.dart';
import '../../../widgets/design_system/background_auth.dart';
import '../../../widgets/design_system/introduction_auth.dart';
import '../../../widgets/responsive/is_mobile.dart';
import 'login_controller.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  static const route = "/login";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            const BackgroundAuth(),
            SingleChildScrollView(
              child: Center(
                child: SizedBox(
                  width: isMobile(context)
                      ? MediaQuery.of(context).size.width * 0.8
                      : MediaQuery.of(context).size.width * 0.5,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 20),
                    child: <PERSON>umn(
                      children: [
                        const IntroductionAuth(
                          text: 'Faça seu login',
                        ),
                        const SizedBox(height: 30),
                        GetBuilder<LoginController>(
                          builder: (control) {
                            return AppTextFormField(
                              title: 'Digite seu e-mail',
                              keyboardType: TextInputType.emailAddress,
                              controller: control.emailController,
                            );
                          },
                        ),
                        const SizedBox(height: 16),
                        GetBuilder<LoginController>(
                          builder: (control) {
                            return AppTextFormField(
                              title: 'Digite sua senha',
                              isPassword: true,
                              controller: control.passwordController,
                              keyboardType: TextInputType.visiblePassword,
                              textInputAction: TextInputAction.done,
                            );
                          },
                        ),
                        const SizedBox(height: 40),
                        GetBuilder<LoginController>(
                          builder: (control) {
                            return AppButtonDefault(
                              onTap: control.loginWithEmailPassword,
                              text: 'Entrar',
                              isValid: control.isFormValid,
                              isLoading: control.isLoading,
                              width: 250,
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
