import 'dart:async';

import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../../shared/mixins/loader_manager.dart';
import '../../../../shared/utils/app_snackbar.dart';

class SendEmailRecoverPasswordController extends GetxController
    with LoaderManagerMixin {
  String? email = Get.arguments;

  Timer? timer;
  int time = 80;

  @override
  void onInit() {
    super.onInit();
    startTimer();
  }

  void startTimer() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (time > 0) {
        time--;
      } else {
        timer.cancel();
      }
      update();
    });
  }

  void resend() async {
    if (email == null) {
      AppSnackbar.to.show('Não foi possível reenviar o email');
      return;
    }

    changeLoading(true);

    await Supabase.instance.client.auth.resetPasswordForEmail(
      email!,
      redirectTo: 'https://appdosaprovados.web.app',
    );

    time = 80;
    startTimer();

    changeLoading(false);
  }

  String formatTime() {
    int minutes = time ~/ 60;
    int remainingSeconds = time % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
