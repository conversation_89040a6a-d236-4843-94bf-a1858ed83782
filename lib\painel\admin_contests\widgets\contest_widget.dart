import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/contest_model.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/confirmation_dialog.dart';
import '../admin_contests_controller.dart';
import 'add_edit_contest/dialog_add_edit_contest.dart';
import 'dialog_contest_info.dart';

class ContestWidget extends StatefulWidget {
  const ContestWidget({super.key, required this.contest});

  final ContestModel contest;

  @override
  State<ContestWidget> createState() => _ContestWidgetState();
}

class _ContestWidgetState extends State<ContestWidget> {
  String imageUrl = '';

  @override
  void initState() {
    super.initState();
    Future.microtask(() {
      setState(() {
        if (widget.contest.image.isNotEmpty) {
          imageUrl = '${widget.contest.image}?t='
              '${DateTime.now().second}';
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        final imageUpdated = await Get.dialog(
          DialogContestInfo(contest: widget.contest),
        );

        if (imageUpdated) {
          imageUrl = '${widget.contest.image}?t='
              '${DateTime.now().millisecondsSinceEpoch}';
          setState(() {});
        }
      },
      splashColor: Colors.transparent,
      highlightColor: Colors.transparent,
      hoverColor: Colors.transparent,
      child: Container(
        height: 250,
        width: 230,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          color: AppColors.secondary,
        ),
        child: Padding(
          padding: const EdgeInsets.all(10),
          child: Column(
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton.outlined(
                    onPressed: () => Get.dialog(
                      DialogAddEditContest(
                        contest: widget.contest,
                      ),
                    ),
                    icon: const Icon(
                      Icons.edit,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(width: 10),
                  GetBuilder<AdminContestsController>(
                    builder: (control) {
                      return IconButton.outlined(
                        onPressed: () {
                          Get.dialog(
                            ConfirmationDialog(
                              message: 'Tem certeza que deseja '
                                  'excluir esse concurso e todos '
                                  'os itens cadastrados nele?',
                              subtitle: 'Essa ação não poderá '
                                  'ser desfeita.',
                              onConfirm: () async {
                                control.deleteContest(
                                  contestId: widget.contest.id!,
                                );
                              },
                            ),
                          );
                        },
                        icon: const Icon(
                          Icons.delete,
                          color: Colors.white,
                        ),
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(height: 10),
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  image: imageUrl.isNotEmpty
                      ? DecorationImage(
                          image: NetworkImage(imageUrl),
                          fit: BoxFit.cover,
                        )
                      : const DecorationImage(
                          image: AssetImage(
                            'assets/images/logo_without_background.png',
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 10),
              Text(
                widget.contest.name,
                style: const TextStyle(
                  fontFamily: 'Roboto',
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: Colors.white,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
