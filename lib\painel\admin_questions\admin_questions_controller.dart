import 'package:get/get.dart';

import '../../app/infra/models/alternative_model.dart';
import '../../app/infra/models/matter_model.dart';
import '../../app/infra/models/question_model.dart';
import '../../app/infra/repositories/matters_repository.dart';
import '../../app/infra/repositories/questions_repository.dart';
import '../../app/shared/mixins/loader_manager.dart';
import '../../app/shared/utils/app_snackbar.dart';

class AdminQuestionsController extends GetxController with LoaderManagerMixin {
  final QuestionsRepository _questionsRepository;
  final MattersRepository _mattersRepository;

  AdminQuestionsController(
    this._questionsRepository,
    this._mattersRepository,
  );

  String? contestName = Get.parameters["cname"];
  String? matterId = Get.parameters["mid"];

  MatterModel? matter;

  List<QuestionModel> questions = [];

  List<String> prefixes = ['A', 'B', 'C', 'D', 'E'];

  @override
  void onInit() async {
    super.onInit();
    await getMatter();
    getQuestions();
  }

  //CRUD ALTERNATIVES
  Future<AlternativeModel?> createAlternative(
    AlternativeModel alternative,
  ) async {
    changeLoading(true);

    final response = await _questionsRepository.createAlternative(
      alternative: alternative,
    );

    changeLoading(false);

    if (response.success) {
      return response.data;
    } else {
      return null;
    }
  }

  Future<AlternativeModel?> updateAlternative(
      AlternativeModel alternative) async {
    changeLoading(true);

    final response = await _questionsRepository.updateAlternative(
      alternative: alternative,
    );

    changeLoading(false);

    if (response.success) {
      return alternative;
    } else {
      return null;
    }
  }

  //CRUD QUESTIONS
  void createQuestion(QuestionModel question) async {
    changeLoading(true);

    final response = await _questionsRepository.createQuestion(
      question: question,
    );

    if (response.success) {
      questions.add(response.data!);
      Get.back();
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  void updateQuestion(QuestionModel question) async {
    changeLoading(true);

    final response = await _questionsRepository.updateQuestion(
      question: question,
    );

    if (response.success) {
      questions.removeWhere((q) => q.id == question.id);
      questions.add(question);
      Get.back();
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  void deleteQuestion({required int questionId}) async {
    changeLoading(true);

    final response = await _questionsRepository.deleteQuestion(
      questionId: questionId,
    );

    if (response.success) {
      questions.removeWhere((q) => q.id == questionId);
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  //GET DATA
  Future<void> getMatter() async {
    if (matterId == null) return;

    changeLoading(true);

    final response = await _mattersRepository.getMatter(matterId: matterId!);

    if (response.success) {
      matter = response.data;
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  void getQuestions() async {
    if (matterId == null) return;

    changeLoading(true);

    final response = await _questionsRepository.getQuestions(
      matterId: matterId!,
    );

    if (response.success) {
      questions = response.data ?? [];
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  Future<List<AlternativeModel>> getAlternatives({
    required String questionId,
  }) async {
    final response = await _questionsRepository.getAlternativesByQuestion(
      questionId: questionId,
    );

    if (!response.success) AppSnackbar.to.show(response.message);

    return response.data ?? [];
  }
}
