class TablePerformanceModel {
  final String matterName;
  final int contestId;
  final int resolved;
  final int hits;
  final int errors;
  final int percentage;

  TablePerformanceModel({
    required this.matterName,
    required this.resolved,
    required this.hits,
    required this.errors,
    required this.percentage,
    required this.contestId,
  });

  factory TablePerformanceModel.fromMap(Map<String, dynamic> map) {
    return TablePerformanceModel(
      matterName: map['matter_name'],
      resolved: map['resolved'],
      contestId: map['contest_id'],
      hits: map['hits'],
      errors: map['errors'],
      percentage: map['percentage'],
    );
  }
}
