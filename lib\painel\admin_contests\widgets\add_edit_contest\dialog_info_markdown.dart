import 'package:flutter/widgets.dart';

import '../../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../../app/widgets/design_system/text_default.dart';

class DialogInfoMarkdown extends StatelessWidget {
  const DialogInfoMarkdown({super.key});

  @override
  Widget build(BuildContext context) {
    return const DialogDefaultWidget(
      title: 'Sobre a formatação',
      form: Padding(
        padding: EdgeInsets.all(15),
        child: TextDefault(
          text: 'Negrito: **texto** ou __texto__\n'
              'Itálico: *texto* ou _texto_\n'
              'Sublinhado: <u>texto</u>',
        ),
      ),
    );
  }
}
