import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';

import '../../shared/utils/ternary_clean.dart';
import '../../themes/app_colors.dart';

class ItemMenuOption extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  final IconData iconT;
  final IconData iconF;
  final String route;
  final IconData? icon;

  const ItemMenuOption({
    super.key,
    required this.title,
    required this.onTap,
    required this.iconT,
    required this.iconF,
    required this.route,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: ListTile(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        onTap: onTap,
        tileColor: ternaryClean(
          condition: Get.currentRoute == route,
          caseTrue: Colors.white70,
          caseFalse: Colors.transparent,
        ),
        leading: Visibility(
          replacement: Icon(
            icon,
            size: 24,
            color: AppColors.secondary,
          ),
          child: ternaryClean(
            condition: Get.currentRoute == route,
            caseTrue: Icon(
              iconT,
              size: 30,
              color: AppColors.secondary,
            ),
            caseFalse: Icon(
              iconF,
              size: 30,
              color: AppColors.backgroundLight,
            ),
          ),
        ),
        title: Text(
          title,
          style: TextStyle(
            fontSize: 15,
            color: ternaryClean(
              condition: Get.currentRoute == route,
              caseTrue: AppColors.secondary,
              caseFalse: AppColors.backgroundLight,
            ),
            fontWeight: FontWeight.w600,
            fontFamily: 'Roboto',
          ),
        ),
      ),
    );
  }
}
