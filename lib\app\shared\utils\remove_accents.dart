String removeAccents(String str) {
  List<String> acentos = [
    "ç",
    "Ç",
    "á",
    "é",
    "í",
    "ó",
    "ú",
    "ý",
    "Á",
    "É",
    "Í",
    "Ó",
    "Ú",
    "Ý",
    "à",
    "è",
    "ì",
    "ò",
    "ù",
    "À",
    "È",
    "Ì",
    "Ò",
    "Ù",
    "ã",
    "õ",
    "ñ",
    "ä",
    "ë",
    "ï",
    "ö",
    "ü",
    "ÿ",
    "Ä",
    "Ë",
    "Ï",
    "Ö",
    "Ü",
    "Ã",
    "Õ",
    "Ñ",
    "â",
    "ê",
    "î",
    "ô",
    "û",
    "Â",
    "Ê",
    "Î",
    "Ô",
    "Û"
  ];
  List<String> semAcento = [
    "c",
    "C",
    "a",
    "e",
    "i",
    "o",
    "u",
    "y",
    "A",
    "E",
    "I",
    "O",
    "U",
    "Y",
    "a",
    "e",
    "i",
    "o",
    "u",
    "A",
    "E",
    "I",
    "O",
    "U",
    "a",
    "o",
    "n",
    "a",
    "e",
    "i",
    "o",
    "u",
    "y",
    "A",
    "E",
    "I",
    "O",
    "U",
    "A",
    "O",
    "N",
    "a",
    "e",
    "i",
    "o",
    "u",
    "A",
    "E",
    "I",
    "O",
    "U"
  ];

  for (int i = 0; i < acentos.length; i++) {
    str = str.replaceAll(acentos[i], semAcento[i]);
  }

  List<String> caracteresEspeciais = [
    "\\.",
    ",",
    "-",
    ":",
    "\\(",
    "\\)",
    "ª",
    "\\|",
    "\\\\",
    "°"
  ];

  for (int i = 0; i < caracteresEspeciais.length; i++) {
    str = str.replaceAll(RegExp(caracteresEspeciais[i]), "");
  }

  str = str.replaceAll(RegExp(r'^\\s+'), "");
  str = str.replaceAll(RegExp(r'\\s+$'), "");
  str = str.replaceAll(RegExp(r'\\s+'), " ");

  return str.toLowerCase().replaceAll(" ", "");
}
