import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../themes/app_colors.dart';
import '../../../../widgets/design_system/text_default.dart';
import '../../recover_password/recover_password_page.dart';

class ForgotPassword extends StatelessWidget {
  const ForgotPassword({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topRight,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 10),
        child: Builder(builder: (context) {
          return TextButton(
            child: const TextDefault(
              text: '<PERSON><PERSON><PERSON> minha senha',
              fontSize: 16,
              color: AppColors.primary,
              fontWeight: FontWeight.w800,
            ),
            onPressed: () {
              Get.toNamed(RecoverPasswordPage.route);
            },
          );
        }),
      ),
    );
  }
}
