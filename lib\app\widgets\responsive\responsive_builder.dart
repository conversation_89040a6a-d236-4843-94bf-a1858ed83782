import 'package:flutter/material.dart';

import '../design_system/app_card_widget.dart';
import '../design_system/text_default.dart';
import 'responsive_util.dart';

class ResponsiveBuilder extends StatelessWidget {
  final List<ResponsiveWidget> children;
  final double spacingWrap;
  final double? height;
  final bool isCard;
  final Axis? direction;

  ResponsiveBuilder({
    super.key,
    required this.children,
    this.spacingWrap = 10,
    this.height,
    this.isCard = true,
    this.direction,
  }) {
    assert(
      children.fold(0.0, (e, child) => e += child.percentScreen) <= 100,
      'A quantidade total de porcentagem da tela precisa dar 100%',
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Wrap(
          direction: direction ?? Axis.horizontal,
          spacing: spacingWrap,
          runSpacing: spacingWrap,
          children: children.map((child) {
            if (child.title == null) {
              return getWidgetWithCardOrNot(child, constraints);
            }
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextDefault(text: child.title!),
                const SizedBox(height: 20),
                getWidgetWithCardOrNot(child, constraints),
              ],
            );
          }).toList(),
        );
      },
    );
  }

  Widget getWidgetWithCardOrNot(
    ResponsiveWidget child,
    BoxConstraints constraints,
  ) {
    Widget myChild = SizedBox(
      height: child.heightFixed ?? height,
      width: child.widthFixed ??
          getResponsiveWidth(constraints, child.percentScreen),
      child: child,
    );
    if (isCard) return AppCardWidget(child: myChild);
    return myChild;
  }

  double getResponsiveWidth(
    BoxConstraints constraints,
    double percentScreen,
  ) {
    return responsiveUtil(
      constraints.maxWidth,
      desktop: constraints.maxWidth * (percentScreen / 100) -
          (((spacingWrap * children.length)) / children.length),
      mobile: constraints.maxWidth,
    );
  }
}

class ResponsiveWidget extends StatelessWidget {
  final double percentScreen;
  final Widget child;
  final String? title;
  final double? widthFixed;
  final double? heightFixed;

  const ResponsiveWidget({
    super.key,
    this.percentScreen = 100,
    required this.child,
    this.title,
    this.heightFixed,
    this.widthFixed,
  });

  @override
  Widget build(BuildContext context) {
    return child;
  }
}
