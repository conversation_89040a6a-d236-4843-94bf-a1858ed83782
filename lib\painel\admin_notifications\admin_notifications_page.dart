import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/themes/app_colors.dart';
import '../../app/widgets/design_system/app_button_default.dart';
import '../../app/widgets/design_system/app_text_form_field.dart';
import '../../app/widgets/design_system/text_default.dart';
import '../../app/widgets/states/app_widget_empty.dart';
import '../../app/widgets/states/app_widget_loading.dart';
import '../widgets/admin_responsive_body.dart';
import 'admin_notifications_controller.dart';

class AdminNotificationsPage extends StatelessWidget {
  const AdminNotificationsPage({Key? key}) : super(key: key);

  static const route = '/notificacoes';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AdminResponsiveBody(
        title: 'Notificações',
        children: [
          GetBuilder<AdminNotificationsController>(
            builder: (controller) {
              return Column(
                children: [
                  AppTextFormField(
                    hintText: "<PERSON><PERSON><PERSON><PERSON> da notificação",
                    controller: controller.titleController,
                  ),
                  const SizedBox(height: 5),
                  AppTextFormField(
                    hintText: "Mensagem da notificação",
                    controller: controller.messageController,
                    maxLines: 3,
                  ),
                  const SizedBox(height: 10),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      AppButtonDefault(
                        text: "Criar notificação",
                        onTap: () {
                          controller.createNotification(
                            title: controller.titleController.text,
                            body: controller.messageController.text,
                          );
                          controller.titleController.clear();
                          controller.messageController.clear();
                        },
                        isValid: true,
                        isLoading: controller.isLoading,
                        width: 200,
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 20),
          GetBuilder<AdminNotificationsController>(
            builder: (controller) {
              if (controller.isLoading) {
                return const AppWidgetLoading();
              }

              if (controller.notifications.isEmpty) {
                return const AppWidgetEmpty(
                  message: 'Nenhuma notificação encontrada',
                  showImage: true,
                );
              }

              return Column(
                children: controller.notifications.map((notification) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 5),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(12),
                        color: AppColors.secondary,
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(20),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Icon(
                              Icons.notifications,
                              color: AppColors.backgroundLight,
                            ),
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    TextDefault(
                                      text: notification.title,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    const SizedBox(height: 2),
                                    TextDefault(
                                      text: notification.body,
                                      fontSize: 12,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                                color: AppColors.primary,
                              ),
                              child: IconButton(
                                onPressed: () => controller.deleteNotification(notification.id),
                                icon: const Icon(
                                  Icons.delete,
                                  color: AppColors.backgroundLight,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              );
            },
          ),
        ],
      ),
    );
  }
}
