import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';
import 'ternary_responsive_web.dart';

class AppPageResponsive extends StatelessWidget {
  final Widget child;
  final String? imageWeb;

  const AppPageResponsive({
    super.key,
    required this.child,
    this.imageWeb,
  });

  @override
  Widget build(BuildContext context) {
    return ternaryResponsiveWeb(
      context: context,
      caseWeb: Center(
        child: Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: ColoredBox(
                    color: AppColors.primary.withOpacity(0.2),
                    child: SizedBox(
                      height: MediaQuery.of(context).size.height,
                      child: Image.asset(
                        imageWeb ?? '',
                        errorBuilder: (context, error, stackTrace) {
                          return const Icon(
                            Icons.info,
                            size: 150,
                            color: AppColors.primary,
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Expanded(child: child),
          ],
        ),
      ),
      caseMobile: child,
    );
  }
}
