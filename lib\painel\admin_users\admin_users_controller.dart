import 'dart:html' as html;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:syncfusion_flutter_xlsio/xlsio.dart' as xlsio;

import '../../app/infra/models/contest_model.dart';
import '../../app/infra/models/defaults/user_model.dart';
import '../../app/infra/repositories/auth_repository.dart';
import '../../app/infra/repositories/contests_repository.dart';
import '../../app/shared/mixins/loader_manager.dart';
import '../../app/shared/utils/app_snackbar.dart';
import '../../app/widgets/design_system/confirmation_dialog.dart';

class AdminUsersController extends GetxController with LoaderManagerMixin {
  final AuthRepository _authRepository;
  final ContestsRepository _contestsRepository;
  AdminUsersController(this._authRepository, this._contestsRepository);

  List<ContestModel> contests = [];

  final emailController = TextEditingController();
  final nameController = TextEditingController();

  @override
  void onInit() {
    nameController.addListener(() => update());
    emailController.addListener(() => update());
    super.onInit();
    getContests();
  }

  Stream<List<Map<String, dynamic>>> getUsersAsStream() {
    return _authRepository.getUsersAsStream(
      emailToSearch: emailController.text,
      nameToSearch: nameController.text,
    );
  }

  void getContests() async {
    final response = await _contestsRepository.getContests();

    if (response.success) contests = response.data ?? [];

    update();
  }

  Future<bool> userHasThisContest({
    required String productId,
    required int contestId,
    required int userId,
    required String email,
  }) async {
    final response = await _contestsRepository.userHasThisContest(
      productId: productId,
      email: email,
      contestId: contestId,
      userId: userId,
    );

    return response.data ?? false;
  }

  Future<bool> releaseOrBlockContestForTheUser({
    required UserModel user,
    required ContestModel contest,
    required bool userHasThisContest,
  }) async {
    userHasThisContest = !userHasThisContest;

    var response = await _contestsRepository.releaseOrBlockContestForTheUser(
      user: user,
      contest: contest,
      userHasThisContest: userHasThisContest,
    );

    if (response.success) return userHasThisContest;
    return !userHasThisContest;
  }

  Future<void> updateIsActive(UserModel value, int id) async {
    var confirm = await Get.dialog(
      ConfirmationDialog(
        message: 'Você deseja '
            '${value.isActive ? 'Desativar' : 'Ativar'} ${value.name} ?',
        onConfirm: () {},
      ),
    );

    if (confirm != true) return;

    if (value.id == id) {
      value.isActive = !value.isActive;
      update();
    }

    final response = await _authRepository.updateIsActive(value);
    if (response.error) {
      value.isActive = !value.isActive;
      AppSnackbar.to.show(response.message);
    } else {
      update();
    }
  }

  DateTime? startAt;
  DateTime? endAt;

  Future<void> exportToExcel() async {
    changeLoading(true);

    // Busca usuários utilizando fetchUsersWithContests
    final users = await _authRepository.fetchUsersWithContests(
      startAt: startAt,
      endAt: endAt,
    );

    if (users.isEmpty) {
      AppSnackbar.to.show("Nenhum usuário encontrado para o período!");
      changeLoading(false);
      return;
    }

    final xlsio.Workbook workbook = xlsio.Workbook();
    final xlsio.Worksheet sheet = workbook.worksheets[0];

    // Título
    final xlsio.Style titleStyle = workbook.styles.add('TitleStyle');
    titleStyle.bold = true;
    titleStyle.fontSize = 16;

    final xlsio.Range titleCell = sheet.getRangeByName('A1');
    titleCell.setText('Relatório de Usuários');
    titleCell.cellStyle = titleStyle;
    sheet.getRangeByName('A1:E1').merge();
    sheet.getRangeByName('A1:E1').cellStyle.hAlign = xlsio.HAlignType.center;
    sheet.getRangeByName('A1:E1').cellStyle.borders.all.lineStyle =
        xlsio.LineStyle.thin;

    // Cabeçalho
    final xlsio.Style headerStyle = workbook.styles.add('HeaderStyle');
    headerStyle.bold = true;

    sheet.getRangeByName('A2').setText('Nome');
    sheet.getRangeByName('B2').setText('WhatsApp');
    sheet.getRangeByName('C2').setText('Registrado Desde');
    sheet.getRangeByName('D2').setText('Email');
    sheet.getRangeByName('E2').setText('Concursos Liberados');
    sheet.getRangeByName('A2:E2').cellStyle = headerStyle;

    for (int col = 1; col <= 5; col++) {
      var headerCell = sheet.getRangeByIndex(2, col);
      headerCell.cellStyle.borders.all.lineStyle = xlsio.LineStyle.thin;
    }

    if (users.isNotEmpty) {
      int row = 3;
      for (var user in users) {
        // Escreve as informações básicas do usuário
        sheet.getRangeByIndex(row, 1).setText(user.name);
        sheet.getRangeByIndex(row, 2).setText(user.phone);
        sheet
            .getRangeByIndex(row, 3)
            .setText(DateFormat('dd/MM/yyyy').format(user.createdAt));
        sheet.getRangeByIndex(row, 4).setText(user.email);

        // Escreve os concursos liberados (já presentes no UserModel)
        final contests = user.contestsLiberated?.join(', ') ?? 'Nenhum';
        sheet.getRangeByIndex(row, 5).setText(contests);

        // Adiciona bordas às células
        for (int col = 1; col <= 5; col++) {
          var cell = sheet.getRangeByIndex(row, col);
          cell.cellStyle.borders.all.lineStyle = xlsio.LineStyle.thin;
        }

        row++;
      }

      // Ajusta a largura das colunas automaticamente
      for (int col = 1; col <= 5; col++) {
        sheet.autoFitColumn(col);
      }
    }

    // Salva e baixa o arquivo Excel para web
    final List<int> bytes = workbook.saveAsStream();
    workbook.dispose();

    final Uint8List data = Uint8List.fromList(bytes);
    downloadReportWeb(data, 'relatorio_usuarios');

    // Mostra mensagem de sucesso
    Get.snackbar('Sucesso', 'Relatório de usuários exportado com sucesso');
    changeLoading(false);
  }

  Future<void> downloadReportWeb(Uint8List data, String fileName) async {
    final blob = html.Blob([data],
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    final url = html.Url.createObjectUrlFromBlob(blob);
    // ignore: unused_local_variable
    final anchor = html.AnchorElement(href: url)
      ..setAttribute('download', '$fileName.xlsx')
      ..click();
    html.Url.revokeObjectUrl(url);
  }
}
