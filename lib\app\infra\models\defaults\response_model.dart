class ResponseModel<T> {
  final T? data;
  final String message;
  final String status;

  bool get success => status == "success";
  bool get warning => status == "warning";
  bool get error => status == "error";

  factory ResponseModel.success(
    T data, {
    String message = "success",
    String status = "success",
  }) {
    return ResponseModel(
      data: data,
      message: message,
      status: status,
    );
  }

  factory ResponseModel.warning(
    String message, {
    String status = "warning",
    T? data,
  }) {
    return ResponseModel(
      data: data,
      message: message,
      status: status,
    );
  }

  factory ResponseModel.error(
    String message, {
    String status = "error",
    T? data,
  }) {
    return ResponseModel(
      data: data,
      message: message,
      status: status,
    );
  }

  ResponseModel({
    required this.data,
    required this.message,
    required this.status,
  });
}
