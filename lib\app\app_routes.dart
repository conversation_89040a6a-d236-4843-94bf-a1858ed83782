import 'package:get/get.dart';

import '../painel/admin_contests/admin_contests_routes.dart';
import '../painel/admin_dashboard/admin_dashboard_route.dart';
import '../painel/admin_notifications/admin_notifications_routes.dart';
import '../painel/admin_open_contests/admin_open_contests_routes.dart';
import '../painel/admin_questions/admin_questions_routes.dart';
import '../painel/admin_users/admin_users_routes.dart';
import 'modules/auth/auth_routes.dart';
import 'modules/reset_password/reset_password_page.dart';
import 'modules/reset_password/success_page.dart';
import 'modules/splash/splash_page.dart';

class AppRoutes {
  AppRoutes._();

  static List<GetPage> pages = [
    GetPage(
      name: SplashPage.route,
      page: () => const SplashPage(),
    ),
    GetPage(
      name: ResetPasswordPage.route,
      page: () => const ResetPasswordPage(),
    ),
    GetPage(
      name: SuccessPage.route,
      page: () => const SuccessPage(),
    ),
    ...adminDashboardRoutes,
    ...adminUsersRoutes,
    ...adminContestsRoutes,
    ...authRoutes,
    ...adminQuestionsRoutes,
    ...adminNotificationsRoutes,
    ...adminOpenContestsRoutes,
  ];
}
