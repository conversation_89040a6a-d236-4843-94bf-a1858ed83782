import 'package:get/get.dart';
import 'package:painel_dos_aprovados/app/shared/mixins/loader_manager.dart';

import '../../app/infra/models/contest_model.dart';
import '../../app/infra/models/open_contest_model.dart';
import '../../app/infra/repositories/contests_repository.dart';
import '../../app/infra/repositories/open_contest_repository.dart';

class AdminOpenContestsController extends GetxController with LoaderManagerMixin {
  final OpenContestsRepository _openContestsRepository;
  final ContestsRepository _contestsRepository;
  AdminOpenContestsController(this._openContestsRepository, this._contestsRepository);

  @override
  void onInit() {
    super.onInit();
    getOpenContests();
    getContests();
  }

  int? selectedContestId;
  void setSelectedContestId(int? contestId) {
    selectedContestId = contestId == -1 ? null : contestId;

    if (selectedContestId == null) {
      openContestsFiltered = _openContests.toList();
    } else {
      openContestsFiltered = _openContests.where((c) => c.contestId == contestId).toList();
    }

    update();
  }

  List<ContestModel> contests = [];
  Future<void> getContests() async {
    changeLoading(true);
    final response = await _contestsRepository.getContests();
    contests = response.data ?? [];
    changeLoading(false);
  }

  List<OpenContestModel> _openContests = [];
  List<OpenContestModel> openContestsFiltered = [];
  Future<void> getOpenContests() async {
    changeLoading(true);
    final response = await _openContestsRepository.getOpenContests();
    _openContests = response.data ?? [];
    setSelectedContestId(selectedContestId);
    changeLoading(false);
  }

  Future<void> createContest(OpenContestModel contest) async {
    changeLoading(true);
    final response = await _openContestsRepository.createOpenContest(contest);
    if (response.success) {
      _openContests.add(response.data!);
      setSelectedContestId(selectedContestId);
      Get.back();
    }
    changeLoading(false);
  }

  Future<void> updateContest(OpenContestModel contest) async {
    changeLoading(true);
    await _openContestsRepository.updateOpenContest(contest);
    _openContests.removeWhere((c) => c.id == contest.id);
    _openContests.add(contest);
    setSelectedContestId(selectedContestId);
    Get.back();
    changeLoading(false);
  }

  Future<void> deleteContest(int id) async {
    changeLoading(true);
    final response = await _openContestsRepository.deleteOpenContest(id);
    if (response.success) {
      _openContests.removeWhere((c) => c.id == id);
      setSelectedContestId(selectedContestId);
      Get.back();
    }
    changeLoading(false);
  }
}
