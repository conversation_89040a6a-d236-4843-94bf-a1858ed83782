import 'package:flutter/material.dart';

import '../states/app_widget_empty.dart';
import '../states/app_widget_error.dart';
import '../states/app_widget_loading.dart';

class AppFutureWidget<T> extends StatelessWidget {
  final Widget Function(T? value) builder;
  final Future<T> future;
  final Widget loadingWidget;

  const AppFutureWidget({
    Key? key,
    required this.builder,
    required this.future,
    this.loadingWidget = const AppWidgetLoading(),
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<T>(
      future: future,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return loadingWidget;
        } else if (!snapshot.hasData) {
          return const AppWidgetEmpty(message: 'Nenhum item encontrado');
        } else if (snapshot.hasError || snapshot.data == null) {
          return const AppWidgetError(
            message: 'Aconteceu um erro, tente de novo mais tarde',
          );
        } else {
          return builder(snapshot.data);
        }
      },
    );
  }
}
