import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/themes/app_colors.dart';
import 'dialog_add_edit_question.dart';

class AddQuestionButton extends StatelessWidget {
  const AddQuestionButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 30, right: 30),
      child: Container(
        decoration: const BoxDecoration(
          color: AppColors.primary,
          shape: BoxShape.circle,
        ),
        child: IconButton(
          onPressed: () => Get.dialog(const DialogAddEditQuestion()),
          icon: const Icon(
            Icons.add,
            size: 35,
            color: AppColors.backgroundLight,
          ),
        ),
      ),
    );
  }
}
