import 'package:flutter/material.dart';

import '../../app/themes/app_colors.dart';
import '../../app/widgets/design_system/text_default.dart';

class InfoItem extends StatelessWidget {
  const InfoItem({super.key, required this.data, required this.value});

  final String data;
  final String value;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextDefault(
          text: data,
          color: AppColors.cardPrimaryLight,
          fontSize: 10,
        ),
        TextDefault(
          text: value.isNotEmpty && value != 'null' ? value : '-',
          color: AppColors.backgroundLight,
          fontSize: 16,
        ),
      ],
    );
  }
}
