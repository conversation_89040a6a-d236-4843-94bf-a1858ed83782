import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../painel/widgets/admin_card_menu_desktop.dart';
import '../../themes/app_colors.dart';
import 'is_mobile.dart';

class ResponsiveBody extends StatelessWidget {
  final String title;
  final List<Widget> children;

  const ResponsiveBody({
    super.key,
    required this.title,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Visibility(
          visible: !isMobile(context),
          replacement: const SizedBox.shrink(),
          child: const AdminCardMenuDesktop(),
        ),
        Expanded(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 30,
                horizontal: 30,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      if (isMobile(context))
                        IconButton(
                          onPressed: () {
                            Get.dialog(
                              const Material(child: AdminCardMenuDesktop()),
                            );
                          },
                          icon: const Icon(Icons.menu),
                        ),
                      Expanded(
                        child: Text(
                          title,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: const TextStyle(
                            fontSize: 38,
                            fontWeight: FontWeight.w700,
                            color: AppColors.backgroundBlack,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  ...children,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
