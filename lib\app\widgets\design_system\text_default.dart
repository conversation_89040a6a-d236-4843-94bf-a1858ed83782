import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';

class TextDefault extends StatelessWidget {
  final String text;
  final double? fontSize;
  final Color? color;
  final FontWeight? fontWeight;
  final TextAlign? textAlign;
  final TextOverflow? overflow;
  final String? fontFamily;

  const TextDefault({
    super.key,
    required this.text,
    this.fontSize,
    this.color,
    this.fontWeight,
    this.textAlign,
    this.overflow,
    this.fontFamily,
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        fontFamily: fontFamily ?? 'Roboto',
        fontSize: fontSize ?? 14,
        color: color ?? AppColors.backgroundLight,
        fontWeight: fontWeight ?? FontWeight.w400,
      ),
      textAlign: textAlign ?? TextAlign.start,
      overflow: overflow,
    );
  }
}
