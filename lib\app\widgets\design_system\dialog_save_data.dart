import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../themes/app_colors.dart';
import 'app_button_default.dart';
import 'text_default.dart';

class DialogSaveData extends StatelessWidget {
  final String text;
  final Function()? onTapSave;
  final bool isLoading;

  const DialogSaveData({
    super.key,
    required this.text,
    this.isLoading = false,
    this.onTapSave,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: TextDefault(
        text: text,
        fontSize: 18,
        color: AppColors.backgroundBlack,
      ),
      actions: [
        AppButtonDefault(
          onTap: onTapSave,
          isValid: true,
          isLoading: isLoading,
          text: 'Salvar',
        ),
        const SizedBox(height: 10),
        AppButtonDefault(
          onTap: Get.back,
          text: 'Cancelar',
          isValid: true,
        ),
      ],
    );
  }
}
