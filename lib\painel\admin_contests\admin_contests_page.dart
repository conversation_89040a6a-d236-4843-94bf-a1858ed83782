import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/widgets/states/app_widget_loading.dart';
import '../widgets/admin_responsive_body.dart';
import 'admin_contests_controller.dart';
import 'widgets/add_contest_button.dart';
import 'widgets/contest_widget.dart';

class AdminContestsPage extends StatelessWidget {
  const AdminContestsPage({super.key});

  static const route = '/concursos';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AdminResponsiveBody(
        title: 'Concursos',
        children: [
          GetBuilder<AdminContestsController>(
            builder: (control) {
              if (control.isLoading) return const AppWidgetLoading();

              control.contests.sort((a, b) => a.order.compareTo(b.order));

              return SizedBox(
                height: MediaQuery.of(context).size.height * 0.8,
                child: SingleChildScrollView(
                  child: Align(
                    alignment: MediaQuery.of(context).size.width < 540
                        ? Alignment.center
                        : Alignment.topLeft,
                    child: Wrap(
                      spacing: 20,
                      runSpacing: 20,
                      alignment: WrapAlignment.center,
                      children: control.contests.map((c) {
                        return ContestWidget(contest: c);
                      }).toList(),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      floatingActionButton: const AddContestButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
    );
  }
}
