import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/alternative_model.dart';
import '../../../app/infra/models/question_model.dart';
import '../../../app/shared/utils/app_snackbar.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/app_card_widget.dart';
import '../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../../app/widgets/design_system/text_default.dart';
import '../../../app/widgets/states/app_widget_loading.dart';
import '../../widgets/info_item.dart';
import '../admin_questions_controller.dart';
import 'dialog_edit_alternative.dart';

class DialogQuestionInfo extends StatefulWidget {
  const DialogQuestionInfo({super.key, required this.question});

  final QuestionModel question;

  @override
  State<DialogQuestionInfo> createState() => _DialogQuestionInfoState();
}

class _DialogQuestionInfoState extends State<DialogQuestionInfo> {
  final controller = Get.find<AdminQuestionsController>();

  bool loading = false;
  List<AlternativeModel> alternativesList = [];
  Map<String, String> alternativesMap = {};

  void getAlternatives() async {
    setState(() => loading = true);

    alternativesList = await controller.getAlternatives(
      questionId: widget.question.id.toString(),
    );

    for (var p in controller.prefixes) {
      alternativesMap.addAll({p: ''});
    }

    for (var a in alternativesList) {
      alternativesMap[a.prefix] = a.description;
    }

    altsVerification();

    setState(() => loading = false);
  }

  void altsVerification() {
    final correctAltsList = alternativesList.where((a) => a.correct);

    if (correctAltsList.isEmpty) {
      AppSnackbar.to.show(
        'Atenção! Essa questão não possui nenhuma '
        'alternativa marcada como correta. Por favor, corrija o problema.',
      );
    }

    if (correctAltsList.length > 1) {
      AppSnackbar.to.show(
        'Atenção! Essa questão possui ${correctAltsList.length} '
        'alternativas marcadas como corretas. Por favor, corrija o problema.',
      );
    }
  }

  @override
  void initState() {
    super.initState();
    Future.microtask(() => getAlternatives());
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: 'Informações da questão',
      height: MediaQuery.of(context).size.height * 0.75,
      form: Expanded(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                spacing: 50,
                runSpacing: 5,
                children: [
                  InfoItem(data: 'Banca: ', value: widget.question.board),
                  InfoItem(
                    data: 'Ano: ',
                    value: widget.question.year.toString(),
                  ),
                ],
              ),
              const SizedBox(height: 10),
              InfoItem(data: 'Enunciado', value: widget.question.name),
              const SizedBox(height: 10),
              InfoItem(
                data: 'Comentário',
                value: widget.question.comment ?? '',
              ),
              const Padding(
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 30),
                child: Divider(thickness: 0.5),
              ),
              const TextDefault(
                text: 'Alternativas',
                color: AppColors.cardPrimaryLight,
                fontSize: 18,
              ),
              if (loading)
                const Padding(
                  padding: EdgeInsets.only(top: 10),
                  child: AppWidgetLoading(),
                ),
              if (!loading)
                Column(
                  children: alternativesMap.entries.map((m) {
                    final prefix = m.key;
                    final description = m.value;
                    final alt = alternativesList.firstWhereOrNull((a) {
                      return a.prefix == prefix;
                    });

                    return Padding(
                      padding: const EdgeInsets.symmetric(vertical: 3),
                      child: AppCardWidget(
                        color: AppColors.cardPrimaryLight,
                        child: Padding(
                          padding: const EdgeInsets.all(10),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                children: [
                                  Container(
                                    width: 30,
                                    height: 30,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppColors.backgroundLight,
                                      border: Border.all(
                                        color: Colors.white,
                                        width: 2.0,
                                      ),
                                    ),
                                    child: Center(
                                      child: TextDefault(
                                        text: prefix,
                                        color: AppColors.backgroundBlack,
                                        fontSize: 18,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  InkWell(
                                    onTap: () async {
                                      AlternativeModel? result =
                                          await Get.dialog(
                                        DialogEditAlternative(
                                          questionId: widget.question.id!,
                                          prefix: prefix,
                                          alternative: alt,
                                        ),
                                      );

                                      if (result != null) {
                                        alternativesMap[prefix] =
                                            result.description;
                                        if (alt != null) {
                                          alternativesList.removeWhere(
                                              (item) => item == alt);
                                        }
                                        alternativesList.add(result);

                                        altsVerification();

                                        setState(() {});
                                      }
                                    },
                                    child: Container(
                                      width: 32,
                                      height: 32,
                                      decoration: const BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: AppColors.backgroundLight,
                                      ),
                                      child: const Icon(
                                        Icons.edit,
                                        size: 22,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: TextDefault(
                                  text: description,
                                  color: AppColors.backgroundBlack,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
