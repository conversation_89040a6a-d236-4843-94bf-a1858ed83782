import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/widgets/states/app_widget_empty.dart';
import '../../app/widgets/states/app_widget_loading.dart';
import '../widgets/admin_responsive_body.dart';
import 'admin_open_contests_controller.dart';
import 'widgets/add_open_contest_button.dart';
import 'widgets/open_contest_widget.dart';

class AdminOpenContestsPage extends StatelessWidget {
  const AdminOpenContestsPage({super.key});

  static const route = '/editais-abertos';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AdminResponsiveBody(
        title: 'Editais Abertos',
        children: [
          GetBuilder<AdminOpenContestsController>(
            builder: (control) {
              return SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    // Bo<PERSON><PERSON> "Todos os concursos"
                    GestureDetector(
                      onTap: () => control.setSelectedContestId(-1),
                      child: Container(
                        margin: const EdgeInsets.only(right: 12),
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 8),
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: control.selectedContestId == -1
                                ? Colors.blue
                                : Colors.grey[600]!,
                            width: 2,
                          ),
                          borderRadius: BorderRadius.circular(8),
                          color: control.selectedContestId == -1
                              ? Colors.blue.withOpacity(0.1)
                              : Colors.transparent,
                        ),
                        child: Text(
                          'Todos os concursos',
                          style: TextStyle(
                            color: control.selectedContestId == -1
                                ? Colors.blue
                                : Colors.white,
                            fontWeight: control.selectedContestId == -1
                                ? FontWeight.bold
                                : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                    // Botões dos concursos
                    ...control.contests.map((contest) {
                      return GestureDetector(
                        onTap: () => control.setSelectedContestId(contest.id),
                        child: Container(
                          margin: const EdgeInsets.only(right: 12),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: control.selectedContestId == contest.id
                                  ? Colors.blue
                                  : Colors.grey[600]!,
                              width: 2,
                            ),
                            borderRadius: BorderRadius.circular(8),
                            color: control.selectedContestId == contest.id
                                ? Colors.blue.withOpacity(0.1)
                                : Colors.transparent,
                          ),
                          child: Text(
                            contest.name,
                            style: TextStyle(
                              color: control.selectedContestId == contest.id
                                  ? Colors.blue
                                  : Colors.white,
                              fontWeight:
                                  control.selectedContestId == contest.id
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ],
                ),
              );
            },
          ),
          const SizedBox(height: 20),
          GetBuilder<AdminOpenContestsController>(
            builder: (control) {
              if (control.isLoading) return const AppWidgetLoading();

              if (control.openContestsFiltered.isEmpty) {
                return const Center(
                  child: AppWidgetEmpty(
                    showImage: true,
                    message: 'Nenhum edital aberto encontrado',
                    description: 'Crie um edital aberto para começar',
                  ),
                );
              }

              return SizedBox(
                height: MediaQuery.of(context).size.height * 0.8,
                child: SingleChildScrollView(
                  child: Align(
                    alignment: MediaQuery.of(context).size.width < 540
                        ? Alignment.center
                        : Alignment.topLeft,
                    child: Wrap(
                      spacing: 20,
                      runSpacing: 20,
                      alignment: WrapAlignment.center,
                      children: control.openContestsFiltered.map((c) {
                        return OpenContestWidget(contest: c);
                      }).toList(),
                    ),
                  ),
                ),
              );
            },
          ),
        ],
      ),
      floatingActionButton: const AddOpenContestButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.endTop,
    );
  }
}
