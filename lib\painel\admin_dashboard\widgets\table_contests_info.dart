import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/widgets/design_system/text_default.dart';
import '../admin_dashboard_controller.dart';

class TableContestsInfo extends StatelessWidget {
  const TableContestsInfo({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<AdminDashboardController>(builder: (control) {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: DataTable(
          columns: const [
            DataColumn(
              label: TextDefault(
                text: 'Concurso',
                fontWeight: FontWeight.w700,
              ),
            ),
            DataColumn(
              label: TextDefault(
                text: 'Qtd. matérias',
                fontWeight: FontWeight.w700,
              ),
            ),
            DataColumn(
              label: TextDefault(
                text: 'Qtd. usuários',
                fontWeight: FontWeight.w700,
              ),
            ),
          ],
          rows: control.contests.map((c) {
            return DataRow(cells: [
              DataCell(
                TextDefault(text: c.name),
              ),
              DataCell(
                Center(
                  child: TextDefault(
                    text: (c.qtMatters ?? 0).toString(),
                  ),
                ),
              ),
              DataCell(
                Center(
                  child: TextDefault(
                    text: (c.qtUsers ?? 0).toString(),
                  ),
                ),
              ),
            ]);
          }).toList(),
        ),
      );
    });
  }
}
