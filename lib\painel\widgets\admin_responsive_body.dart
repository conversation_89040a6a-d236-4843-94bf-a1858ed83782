import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../app/themes/app_colors.dart';
import '../../app/widgets/responsive/is_mobile.dart';
import 'admin_card_menu_desktop.dart';

class AdminResponsiveBody extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final VoidCallback? onTapIconAdd;

  const AdminResponsiveBody({
    super.key,
    required this.title,
    required this.children,
    this.onTapIconAdd,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Visibility(
          visible: !isMobile(context),
          replacement: const SizedBox.shrink(),
          child: const AdminCardMenuDesktop(),
        ),
        Expanded(
          child: SingleChildScrollView(
            physics: const BouncingScrollPhysics(),
            child: Container(
              padding: const EdgeInsets.symmetric(
                vertical: 30,
                horizontal: 30,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      if (isMobile(context))
                        IconButton(
                          onPressed: () {
                            Get.dialog(
                              const Material(
                                child: AdminCardMenuDesktop(),
                              ),
                            );
                          },
                          icon: const Icon(Icons.menu),
                        ),
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: 30,
                          fontWeight: FontWeight.w600,
                          color: AppColors.backgroundLight,
                        ),
                      ),
                      const SizedBox(width: 20),
                      if (onTapIconAdd != null)
                        CircleAvatar(
                          child: IconButton(
                            onPressed: onTapIconAdd,
                            icon: const Icon(Icons.add),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 30),
                  ...children,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
