import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../painel/admin_dashboard/admin_dashboard_page.dart';
import '../auth/login/login_page.dart';
import '../reset_password/reset_password_page.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  static const route = '/splash';

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  bool loading = true;

  @override
  void initState() {
    super.initState();
    Future.microtask(() async {
      Future.delayed(const Duration(seconds: 3)).then((value) async {
        if (Uri.base.hasQuery) {
          Get.offAllNamed(ResetPasswordPage.route);
        } else {
          if (Supabase.instance.client.auth.currentUser != null) {
            Get.offAllNamed(AdminDashboardPage.route);
          } else {
            Get.offAllNamed(LoginPage.route);
          }
        }
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Center(
        child: Image.asset(
          "assets/images/logo_without_background.png",
        ),
      ),
    );
  }
}
