class MatterModel {
  final int? id;
  final String name;
  final String description;
  final DateTime createdAt;
  final int contestId;
  final int order;
  final bool needPay;

  int? qtdQuestions;

  MatterModel({
    this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.contestId,
    required this.order,
    required this.needPay,
    this.qtdQuestions,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'description': description,
      'created_at': createdAt.toIso8601String(),
      'contest_id': contestId,
      'order': order,
      'need_pay': needPay,
    };
  }

  factory MatterModel.fromMap(Map<String, dynamic> map) {
    return MatterModel(
      id: map['id'],
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
      contestId: map['contest_id'],
      order: map['order'] ?? map['id'],
      needPay: map['need_pay'] ?? true,
    );
  }
}
