import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:painel_dos_aprovados/painel/admin_notifications/admin_notifications_page.dart';
import 'package:painel_dos_aprovados/painel/admin_open_contests/admin_open_contests_page.dart';

import '../../app/infra/repositories/auth_repository.dart';
import '../../app/modules/auth/login/login_page.dart';
import '../../app/widgets/design_system/app_card_widget.dart';
import '../../app/widgets/design_system/confirmation_dialog.dart';
import '../../app/widgets/responsive/item_menu_option.dart';
import '../admin_contests/admin_contests_page.dart';
import '../admin_dashboard/admin_dashboard_page.dart';
import '../admin_users/admin_users_page.dart';

class AdminCardMenuDesktop extends StatelessWidget {
  const AdminCardMenuDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    return AppCardWidget(
      elevation: 0,
      width: MediaQuery.of(context).size.width * 0.22,
      child: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            const SizedBox(height: 20),
            Image.asset(
              "assets/images/logo_without_background.png",
              scale: 2.5,
            ),
            const SizedBox(height: 10),
            ItemMenuOption(
              title: 'Dashboard',
              iconF: Icons.home_filled,
              iconT: Icons.home_filled,
              route: AdminDashboardPage.route,
              onTap: () => Get.toNamed(AdminDashboardPage.route),
            ),
            ItemMenuOption(
              title: 'Concursos',
              iconF: Icons.library_books,
              iconT: Icons.library_books,
              route: AdminContestsPage.route,
              onTap: () => Get.toNamed(AdminContestsPage.route),
            ),
            ItemMenuOption(
              title: 'Usuários',
              iconF: Icons.people,
              iconT: Icons.people,
              route: AdminUsersPage.route,
              onTap: () => Get.toNamed(AdminUsersPage.route),
            ),
            ItemMenuOption(
              title: 'Notificações',
              iconF: Icons.notifications,
              iconT: Icons.notifications,
              route: AdminNotificationsPage.route,
              onTap: () => Get.toNamed(AdminNotificationsPage.route),
            ),
            ItemMenuOption(
              title: 'Editais Abertos',
              iconF: Icons.menu_book,
              iconT: Icons.menu_book,
              route: AdminOpenContestsPage.route,
              onTap: () => Get.toNamed(AdminOpenContestsPage.route),
            ),
            const Spacer(),
            ItemMenuOption(
              title: 'Sair da conta',
              iconF: Icons.logout,
              iconT: Icons.logout,
              route: '',
              onTap: () {
                Get.dialog(
                  ConfirmationDialog(
                    message: 'Tem certeza que deseja sair da sua conta?',
                    onConfirm: () async {
                      final authRepo = AuthRepository();

                      await authRepo.logoutUser();
                      Get.offAllNamed(LoginPage.route);
                    },
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
