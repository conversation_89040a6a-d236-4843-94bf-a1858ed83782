import 'package:flutter/material.dart';

import '../../themes/app_colors.dart';

class AppWidgetLoading extends StatelessWidget {
  const AppWidgetLoading({
    super.key,
    this.color = AppColors.primary,
  });

  final Color color;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 50,
        height: 50,
        child: CircularProgressIndicator.adaptive(
          strokeWidth: 3.5,
          backgroundColor: color.withOpacity(0.5),
          valueColor: AlwaysStoppedAnimation(color),
        ),
      ),
    );
  }
}
