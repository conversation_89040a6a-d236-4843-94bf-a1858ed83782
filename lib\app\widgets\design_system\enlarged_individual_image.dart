import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../themes/app_colors.dart';

class EnlargedImage extends StatelessWidget {
  const EnlargedImage({super.key, required this.imageUrl});

  final String imageUrl;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(30),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            IconButton.outlined(
              onPressed: Get.back,
              icon: const Icon(
                Icons.close,
                color: AppColors.cardSecondaryLight,
              ),
            ),
            const SizedBox(height: 20),
            <PERSON>(
              tag: imageUrl,
              child: Image.network(imageUrl),
            ),
          ],
        ),
      ),
    );
  }
}
