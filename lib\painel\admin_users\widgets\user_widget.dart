import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/infra/models/defaults/user_model.dart';
import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/text_default.dart';
import 'dialog_info.dart';

class UserWidget extends StatelessWidget {
  const UserWidget({
    Key? key,
    required this.user,
    required this.onIsActive,
  }) : super(key: key);

  final UserModel user;
  final void Function(bool value) onIsActive;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: AppColors.secondary,
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Icon(
                Icons.person,
                color: AppColors.backgroundLight,
              ),
              Expanded(
                child: InkWell(
                  onTap: () => Get.dialog(DialogInfo(user: user)),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        TextDefault(
                          text: user.name,
                          fontWeight: FontWeight.bold,
                        ),
                        const SizedBox(height: 2),
                        TextDefault(
                          text: '${user.email}\n${user.phone}',
                          fontSize: 12,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Container(
                height: 40,
                margin: const EdgeInsets.only(right: 5),
                padding: const EdgeInsets.only(left: 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: AppColors.primary,
                ),
                child: Row(
                  children: [
                    TextDefault(text: user.isActive ? 'Ativado' : 'Desativado'),
                    Switch(
                      trackOutlineColor:
                          const WidgetStatePropertyAll(Colors.transparent),
                      inactiveTrackColor: AppColors.primaryRed,
                      inactiveThumbColor: AppColors.backgroundLight,
                      activeTrackColor: AppColors.primaryGreen,
                      value: user.isActive,
                      onChanged: (value) => onIsActive(value),
                    ),
                  ],
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: AppColors.primary,
                ),
                child: IconButton(
                  onPressed: () => Get.dialog(DialogInfo(user: user)),
                  icon: const Icon(
                    Icons.info_outline,
                    color: AppColors.backgroundLight,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
