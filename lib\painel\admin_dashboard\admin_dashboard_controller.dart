import 'package:get/get.dart';

import '../../app/infra/models/contest_model.dart';
import '../../app/infra/models/dashboard_users_model.dart';
import '../../app/infra/models/ranking/ranking_item_model.dart';
import '../../app/infra/repositories/dashboard_repository.dart';
import '../../app/shared/mixins/loader_manager.dart';
import '../../app/shared/utils/app_snackbar.dart';
import '../../app/shared/utils/get_ranking_positions.dart';

class AdminDashboardController extends GetxController with LoaderManagerMixin {
  final DashboardRepository _dashboardRepository;
  AdminDashboardController(this._dashboardRepository);

  List<ContestModel> contests = [];

  DashboardUsersModel? usersInfo;
  int matters = 0;
  int questions = 0;

  @override
  void onInit() {
    super.onInit();
    getData();
    getContests();
  }

  void getData() async {
    changeLoading(true);

    usersInfo = await getCountUsers();
    matters = await getCountMatters();
    questions = await getCountQuestions();

    changeLoading(false);
  }

  void getContests() async {
    changeLoading(true);

    final response = await _dashboardRepository.getContests();

    if (response.success) {
      contests = response.data ?? [];
    } else {
      AppSnackbar.to.show(response.message);
    }

    changeLoading(false);
  }

  Future<int> getCountMatters() async {
    final response = await _dashboardRepository.getCountMatters();
    return response.data ?? 0;
  }

  Future<int> getCountQuestions() async {
    final response = await _dashboardRepository.getCountQuestions();
    return response.data ?? 0;
  }

  Future<DashboardUsersModel?> getCountUsers() async {
    final response = await _dashboardRepository.getCountUsers();
    return response.data;
  }

  Future<List<RankingItemModel>> getLastMonthRankinget() async {
    var list = await _dashboardRepository.getLastMonthRanking();
    return getPositions(list);
  }
}
