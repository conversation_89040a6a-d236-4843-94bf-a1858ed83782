import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../app/infra/models/contest_model.dart';
import '../../../../app/infra/models/open_contest_model.dart';
import '../../../../app/themes/app_colors.dart';
import '../../../../app/widgets/design_system/app_button_default.dart';
import '../../../../app/widgets/design_system/app_text_form_field.dart';
import '../../../../app/widgets/design_system/dialog_default_widget.dart';
import '../../admin_open_contests_controller.dart';

class DialogAddEditOpenContest extends StatefulWidget {
  const DialogAddEditOpenContest({super.key, this.contest});

  final OpenContestModel? contest;

  @override
  State<DialogAddEditOpenContest> createState() =>
      _DialogAddEditOpenContestState();
}

class _DialogAddEditOpenContestState extends State<DialogAddEditOpenContest> {
  final _formKey = GlobalKey<FormState>();

  ContestModel? selectedContest;
  final controller = Get.find<AdminOpenContestsController>();
  // Map para definir nomes específicos baseados no ID do concurso
  final Map<int, String> contestNames = {
    23: "Guarda Municipal",
    18: "Polícia Militar",
    20: "Polícia Penal",
    19: "Polícia Civil",
    21: "Polícia Rodoviária Federal",
    17: "Polícia Federal",
  };

  final nameController = TextEditingController();
  final stateController = TextEditingController();
  final salaryController = TextEditingController();
  final vacanciesController = TextEditingController();
  final examBoardController = TextEditingController();
  final cnhController = TextEditingController();
  final ageController = TextEditingController();
  final subscriptionDateController = TextEditingController();
  final examDateController = TextEditingController();

  final actionTitleController = TextEditingController();
  final actionLinkController = TextEditingController();

  bool loading = false;

  @override
  void initState() {
    super.initState();
    nameController.addListener(() => setState(() {}));
    stateController.addListener(() => setState(() {}));
    salaryController.addListener(() => setState(() {}));
    vacanciesController.addListener(() => setState(() {}));
    examBoardController.addListener(() => setState(() {}));
    cnhController.addListener(() => setState(() {}));
    ageController.addListener(() => setState(() {}));
    subscriptionDateController.addListener(() => setState(() {}));
    examDateController.addListener(() => setState(() {}));
    actionTitleController.addListener(() => setState(() {}));
    actionLinkController.addListener(() => setState(() {}));

    Future.microtask(() {
      if (widget.contest != null) {
        nameController.text = widget.contest!.name;
        stateController.text = widget.contest!.state;
        salaryController.text = widget.contest!.salary.toString();
        vacanciesController.text = widget.contest!.vacancies;
        examBoardController.text = widget.contest!.examBoard;
        cnhController.text = widget.contest!.cnh;
        ageController.text = widget.contest!.age;
        subscriptionDateController.text = widget.contest!.subscriptionDate;
        examDateController.text = widget.contest!.examDate;
        actionTitleController.text = widget.contest!.action['title'] ?? '';
        actionLinkController.text = widget.contest!.action['link'] ?? '';
        selectedContest = controller.contests
            .firstWhereOrNull((c) => c.id == widget.contest!.contestId);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return DialogDefaultWidget(
      title: widget.contest != null ? 'Editar edital' : 'Adicionar edital',
      height: MediaQuery.of(context).size.height * 0.75,
      form: Expanded(
        child: SingleChildScrollView(
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 10),
                GetBuilder<AdminOpenContestsController>(
                  builder: (control) {
                    return DropdownButtonFormField<int>(
                      value: selectedContest?.id,
                      dropdownColor: AppColors.secondary,
                      style: const TextStyle(color: Colors.white),
                      decoration: const InputDecoration(
                        hintText: 'Selecione um concurso',
                        labelText: 'Vincular Concurso',
                        labelStyle: TextStyle(color: Colors.white),
                        border: OutlineInputBorder(),
                      ),
                      items: control.contests.map((contest) {
                        return DropdownMenuItem<int>(
                          value: contest.id,
                          child: Text(contest.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          selectedContest = control.contests
                              .firstWhereOrNull((c) => c.id == value);
                          // Usa o map para definir o nome baseado no ID do concurso
                          nameController.text =
                              contestNames[selectedContest?.id] ?? '';
                        });
                      },
                    );
                  },
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Nome',
                  controller: nameController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Estado',
                  controller: stateController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Salário',
                  controller: salaryController,
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Vagas',
                  controller: vacanciesController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Banca',
                  controller: examBoardController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'CNH',
                  controller: cnhController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Idade',
                  controller: ageController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Data de inscrição',
                  controller: subscriptionDateController,
                ),
                const SizedBox(height: 10),
                AppTextFormField(
                  title: 'Data da prova',
                  controller: examDateController,
                ),
                const SizedBox(height: 10),
                Row(
                  children: [
                    Expanded(
                      child: AppTextFormField(
                        title: 'Título do botão de ação',
                        controller: actionTitleController,
                      ),
                    ),
                    const SizedBox(width: 10),
                    Expanded(
                      child: AppTextFormField(
                        title: 'Link do botão de ação',
                        controller: actionLinkController,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 30),
                AppButtonDefault(
                  isLoading: loading,
                  isValid: _formKey.currentState?.validate(),
                  width: double.maxFinite,
                  onTap: () async {
                    setState(() => loading = true);

                    final contest = OpenContestModel(
                      id: widget.contest?.id,
                      createdAt: widget.contest?.createdAt ?? DateTime.now(),
                      name: nameController.text.trim(),
                      state: stateController.text.trim(),
                      salary: salaryController.text.trim(),
                      vacancies: vacanciesController.text.trim(),
                      examBoard: examBoardController.text.trim(),
                      cnh: cnhController.text.trim(),
                      age: ageController.text.trim(),
                      subscriptionDate: subscriptionDateController.text.trim(),
                      examDate: examDateController.text.trim(),
                      action: {
                        'title': actionTitleController.text.trim(),
                        'link': actionLinkController.text.trim(),
                      },
                      contestId: selectedContest?.id,
                    );

                    if (widget.contest != null) {
                      await controller.updateContest(
                        contest,
                      );
                    } else {
                      await controller.createContest(
                        contest,
                      );
                    }

                    setState(() => loading = false);
                  },
                  text: widget.contest != null
                      ? 'Salvar alterações'
                      : 'Adicionar',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
