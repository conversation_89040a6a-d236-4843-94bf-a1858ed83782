import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../app/themes/app_colors.dart';
import '../../../app/widgets/design_system/text_default.dart';
import '../admin_questions_controller.dart';

class InfoAndButtonBack extends StatelessWidget {
  const InfoAndButtonBack({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          padding: EdgeInsets.zero,
          onPressed: Get.back,
          icon: Card(
            margin: EdgeInsets.zero,
            color: AppColors.primary,
            shape: OutlineInputBorder(
              borderRadius: BorderRadius.circular(6),
            ),
            child: const Padding(
              padding: EdgeInsets.only(
                right: 5,
                left: 15,
                top: 5,
                bottom: 5,
              ),
              child: Icon(
                Icons.arrow_back_ios,
                color: AppColors.backgroundLight,
                size: 30,
              ),
            ),
          ),
        ),
        const SizedBox(width: 10),
        Expanded(
          child: GetBuilder<AdminQuestionsController>(
            builder: (control) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextDefault(
                    text: 'Concurso: ${control.contestName ?? '-'}',
                    color: AppColors.backgroundLight,
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                  TextDefault(
                    text: 'Matéria: ${control.matter?.name ?? '-'}',
                    color: AppColors.backgroundLight,
                    fontSize: 16,
                  ),
                ],
              );
            },
          ),
        ),
      ],
    );
  }
}
