import 'package:get/get.dart';

import '../../app/infra/repositories/auth_repository.dart';
import '../../app/infra/repositories/contests_repository.dart';
import '../../app/shared/middlewares/auth_middleware.dart';
import 'admin_users_controller.dart';
import 'admin_users_page.dart';

List<GetPage> adminUsersRoutes = [
  GetPage(
    name: AdminUsersPage.route,
    page: () => const AdminUsersPage(),
    binding: BindingsBuilder(() {
      Get.lazyPut(() => AuthRepository());
      Get.lazyPut(() => ContestsRepository());
      Get.put(AdminUsersController(Get.find(), Get.find()));
    }),
    middlewares: [AuthMiddleware()],
  ),
];
