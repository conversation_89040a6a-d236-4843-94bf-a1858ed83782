import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../app/themes/app_colors.dart';
import '../../../../app/widgets/states/app_widget_loading.dart';
import '../../admin_contests_controller.dart';

class ContestImageWidget extends StatefulWidget {
  const ContestImageWidget({
    super.key,
    required this.imageFile,
    required this.imageUrl,
    required this.isEdit,
    required this.onTap,
    required this.isLoading,
  });

  final void Function() onTap;
  final PlatformFile? imageFile;
  final String imageUrl;
  final bool isLoading;
  final bool isEdit;

  @override
  State<ContestImageWidget> createState() => _ContestImageWidgetState();
}

class _ContestImageWidgetState extends State<ContestImageWidget> {
  final controller = Get.find<AdminContestsController>();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => widget.onTap(),
      child: Builder(
        builder: (context) {
          if (widget.isLoading) return const AppWidgetLoading();

          if (widget.isEdit) {
            return Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: widget.imageUrl.isEmpty && widget.imageFile == null
                    ? Colors.grey
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(12),
                image: widget.imageFile != null
                    ? DecorationImage(
                        image: MemoryImage(widget.imageFile!.bytes!),
                        fit: BoxFit.cover,
                      )
                    : widget.imageUrl.isNotEmpty
                        ? DecorationImage(
                            image: NetworkImage(widget.imageUrl),
                            fit: BoxFit.cover,
                          )
                        : null,
              ),
              child: widget.imageUrl.isEmpty && widget.imageFile == null
                  ? const Center(
                      child: Icon(
                        Icons.add,
                        color: AppColors.primary,
                        size: 40,
                      ),
                    )
                  : null,
            );
          }

          return Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              color:
                  widget.imageFile != null ? Colors.transparent : Colors.grey,
              borderRadius: BorderRadius.circular(12),
              image: widget.imageFile != null
                  ? DecorationImage(
                      image: MemoryImage(widget.imageFile!.bytes!),
                      fit: BoxFit.cover,
                    )
                  : null,
            ),
            child: widget.imageFile != null
                ? null
                : const Center(
                    child: Icon(
                      Icons.add,
                      color: AppColors.primary,
                      size: 40,
                    ),
                  ),
          );
        },
      ),
    );
  }
}
