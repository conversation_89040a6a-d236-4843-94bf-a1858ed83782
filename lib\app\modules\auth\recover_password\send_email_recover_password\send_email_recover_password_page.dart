import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../widgets/design_system/app_button_default.dart';
import '../../../../widgets/design_system/background_auth.dart';
import '../../../../widgets/design_system/introduction_auth.dart';
import '../../../../widgets/design_system/text_default.dart';
import '../../login/login_page.dart';
import 'send_email_recover_password_controller.dart';
import 'widgets/resend_button.dart';

class SendEmailRecoverPasswordPage extends StatelessWidget {
  const SendEmailRecoverPasswordPage({super.key});

  static const route = "/send-email-recover-password";

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            const BackgroundAuth(),
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const Center(
                      child: Introduction<PERSON><PERSON>(text: 'E-mail enviado!'),
                    ),
                    const SizedBox(height: 30),
                    const TextDefault(
                      text: 'Por favor cheque sua caixa de e-mail\'s',
                      fontSize: 20,
                      fontWeight: FontWeight.w300,
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 30),
                    GetBuilder<SendEmailRecoverPasswordController>(
                      builder: (control) {
                        return ResendButton(valid: control.time == 0);
                      },
                    ),
                    const SizedBox(height: 30),
                    AppButtonDefault(
                      text: 'Fazer login',
                      isValid: true,
                      onTap: () => Get.offNamed(LoginPage.route),
                      width: 160,
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
